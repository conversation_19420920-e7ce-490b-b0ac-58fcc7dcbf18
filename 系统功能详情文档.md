# 印刷品商城系统功能详情文档

## 系统概述

### 项目信息
- **项目名称**: 印刷品商城系统
- **技术栈**: Flask + MySQL + Bootstrap + jQuery
- **架构模式**: MVC架构，蓝图模块化
- **部署方式**: Python WSGI应用
- **访问地址**: http://127.0.0.1:5000

### 系统特色
- 🎨 **现代化UI设计** - 玻璃态效果、渐变色彩、响应式布局
- 🔧 **灵活的属性系统** - 支持公式计算、数量折扣、动态定价
- 💰 **多种支付方式** - 支付宝、微信支付集成
- 📱 **移动端适配** - 完全响应式设计
- 🛡️ **安全防护** - CSRF保护、用户权限管理

## 核心功能模块

### 1. 用户认证系统 (auth)

#### 1.1 用户注册
- **路由**: `/register`
- **方法**: GET, POST
- **功能**: 新用户注册账户
- **字段**: 用户名、邮箱、真实姓名、手机号、密码
- **验证**: 用户名唯一性、邮箱格式、密码强度

#### 1.2 用户登录
- **路由**: `/login`
- **方法**: GET, POST
- **功能**: 用户身份验证
- **特性**: 
  - 记住登录状态
  - 自动跳转到目标页面
  - 登录时间记录
  - 账户状态检查

#### 1.3 用户退出
- **路由**: `/logout`
- **方法**: GET
- **功能**: 清除用户会话

### 2. 商品展示系统 (main)

#### 2.1 首页展示
- **路由**: `/`
- **模板**: `main/index_modern.html`
- **功能**: 
  - 现代化首页设计
  - 特色商品展示
  - 联系信息展示
  - 服务介绍

#### 2.2 商品列表
- **路由**: `/products`
- **方法**: GET
- **功能**: 
  - 商品分页展示
  - 分类筛选
  - 关键词搜索
  - 排序功能（价格、时间、热度）
- **参数**:
  - `page`: 页码
  - `category`: 分类ID
  - `search`: 搜索关键词
  - `sort`: 排序方式

#### 2.3 商品详情
- **路由**: `/product/<int:id>`
- **方法**: GET
- **功能**:
  - 商品详细信息
  - 属性选择器
  - 价格计算器
  - 数量折扣显示
  - 图片轮播

### 3. 价格计算API

#### 3.1 动态价格计算
- **路由**: `/api/calculate-price`
- **方法**: POST
- **功能**: 实时计算商品价格
- **请求参数**:
```json
{
  "product_id": 1,
  "quantity": 10,
  "selected_attributes": [1, 2, 3],
  "attribute_quantities": {"1": 50}
}
```
- **响应格式**:
```json
{
  "unit_price": 12.50,
  "total_price": 125.00,
  "discount_rate": 0.1,
  "breakdown": {
    "base_price": 10.00,
    "attribute_cost": 2.50,
    "quantity_discount": -1.25
  }
}
```

### 4. 订单管理系统

#### 4.1 订单列表
- **路由**: `/orders`
- **方法**: GET
- **功能**: 用户订单历史查看
- **状态**: pending, paid, processing, shipped, delivered, cancelled

#### 4.2 订单详情
- **路由**: `/order/<int:id>`
- **方法**: GET
- **功能**: 订单详细信息查看

#### 4.3 订单操作API

##### 取消订单
- **路由**: `/api/orders/<int:id>/cancel`
- **方法**: POST
- **功能**: 取消待支付订单
- **权限**: 订单所有者

##### 同步支付状态
- **路由**: `/api/orders/<int:id>/sync-payment`
- **方法**: POST
- **功能**: 手动同步支付状态

### 5. 支付系统

#### 5.1 支付页面
- **路由**: `/payment/<int:order_id>`
- **方法**: GET
- **功能**: 支付方式选择页面

#### 5.2 发起支付API
- **路由**: `/api/orders/<int:id>/pay`
- **方法**: POST
- **功能**: 创建支付订单
- **支持方式**: 
  - 支付宝 (alipay)
  - 微信支付 (wxpay)
- **请求参数**:
```json
{
  "payment_type": "alipay"
}
```
- **响应格式**:
```json
{
  "success": true,
  "payment_url": "https://payment-gateway.com/pay?...",
  "order_no": "ORDER20241215001"
}
```

#### 5.3 支付配置
- **网关**: 易支付系统
- **配置项**:
  - `EPAY_GATEWAY`: 支付网关地址
  - `EPAY_PID`: 商户ID
  - `EPAY_KEY`: 商户密钥
  - `EPAY_SUBMIT_URL`: 支付提交地址
  - `EPAY_API_URL`: API接口地址

### 6. 购物车系统

#### 6.1 购物车模型
- **表名**: cart_items
- **字段**:
  - user_id: 用户ID
  - product_id: 商品ID
  - quantity: 数量
  - selected_attributes: 选择的属性(JSON)

#### 6.2 购物车方法
- `get_cart_count()`: 获取购物车商品种类数
- `get_cart_total_quantity()`: 获取购物车商品总数量

## 管理后台系统 (admin)

### 1. 仪表盘
- **路由**: `/admin/`
- **功能**: 
  - 系统统计数据
  - 最近订单
  - 最新商品
  - 快速操作入口

### 2. 商品管理

#### 2.1 分类管理
- **路由**: `/admin/categories`
- **功能**:
  - 分类CRUD操作
  - 层级分类支持
  - 排序管理
  - 批量操作

#### 2.2 属性管理
- **路由**: `/admin/attribute-groups`
- **功能**:
  - 属性组管理
  - 属性项管理
  - 价格修饰符设置
  - 公式计算配置

#### 2.3 商品管理
- **路由**: `/admin/products`
- **功能**:
  - 商品CRUD操作
  - 图片上传管理
  - 属性绑定
  - 数量折扣设置

### 3. 订单管理
- **路由**: `/admin/orders`
- **功能**:
  - 订单列表查看
  - 订单状态管理
  - 订单搜索筛选
  - 订单详情查看

### 4. 用户管理
- **路由**: `/admin/users`
- **功能**:
  - 用户列表管理
  - 用户状态控制
  - 权限管理
  - 用户详情查看

### 5. 系统设置
- **路由**: `/admin/settings`
- **功能**:
  - 网站基本信息
  - 运费设置
  - 支付配置
  - 邮件配置

## API接口汇总

### 前台API
| 接口路径 | 方法 | 功能 | 权限 |
|---------|------|------|------|
| `/api/calculate-price` | POST | 价格计算 | 无 |
| `/api/orders/<id>/cancel` | POST | 取消订单 | 登录用户 |
| `/api/orders/<id>/pay` | POST | 发起支付 | 登录用户 |
| `/api/orders/<id>/sync-payment` | POST | 同步支付状态 | 登录用户 |

### 后台API
| 接口路径 | 方法 | 功能 | 权限 |
|---------|------|------|------|
| `/admin/api/categories/<id>/delete` | DELETE | 删除分类 | 管理员 |
| `/admin/api/attributes-by-category/<id>` | GET | 获取分类属性 | 管理员 |
| `/admin/api/products/<id>/delete` | DELETE | 删除商品 | 管理员 |
| `/admin/api/orders/<id>/update-status` | POST | 更新订单状态 | 管理员 |

## 数据库设计

### 核心表结构
- **users**: 用户表
- **categories**: 商品分类表
- **products**: 商品表
- **attribute_groups**: 属性组表
- **attributes**: 属性表
- **product_attributes**: 商品属性关联表
- **orders**: 订单表
- **order_items**: 订单项表
- **cart_items**: 购物车表
- **quantity_discounts**: 数量折扣表
- **system_configs**: 系统配置表

### 关键特性
- **层级分类**: 支持无限级分类
- **灵活属性**: 属性组+属性项的二级结构
- **价格计算**: 支持固定价格和公式计算
- **数量折扣**: 支持阶梯定价
- **订单状态**: 完整的订单生命周期管理

## 安全特性

### 1. 身份认证
- Flask-Login会话管理
- 密码哈希存储(PBKDF2)
- 登录状态验证

### 2. 权限控制
- 管理员权限装饰器
- 用户资源访问控制
- API接口权限验证

### 3. 数据安全
- CSRF保护
- SQL注入防护
- XSS防护
- 文件上传安全

## 部署配置

### 环境变量
```bash
FLASK_CONFIG=production
DATABASE_URL=mysql://user:pass@host/db
SECRET_KEY=your-secret-key
EPAY_GATEWAY=https://your-payment-gateway.com/
EPAY_PID=your-merchant-id
EPAY_KEY=your-merchant-key
```

### 依赖包
- Flask: Web框架
- Flask-SQLAlchemy: ORM
- Flask-Login: 用户认证
- Flask-WTF: 表单处理
- PyMySQL: MySQL驱动
- Pillow: 图片处理

## 技术实现细节

### 1. 支付系统实现

#### PaymentService类
```python
class PaymentService:
    def __init__(self):
        self.gateway = current_app.config.get('EPAY_GATEWAY')
        self.pid = current_app.config.get('EPAY_PID')
        self.key = current_app.config.get('EPAY_KEY')
        self.submit_url = current_app.config.get('EPAY_SUBMIT_URL')
        self.api_url = current_app.config.get('EPAY_API_URL')

    def create_payment(self, order_no, amount, product_name, return_url, notify_url, payment_type='alipay'):
        """创建支付订单"""
        # 构建支付参数和签名
        # 返回支付URL

    def verify_notify(self, params):
        """验证支付回调"""
        # 验证签名
        # 返回验证结果
```

#### 支付流程
1. **用户选择支付方式** → 前端发送支付请求
2. **后端创建支付订单** → 调用PaymentService.create_payment()
3. **生成支付URL** → 重定向到支付网关
4. **用户完成支付** → 支付网关回调通知
5. **验证支付结果** → 更新订单状态

### 2. 价格计算引擎

#### 计算逻辑
```python
def calculate_price():
    # 1. 获取商品基础价格
    base_price = product.base_price

    # 2. 计算属性附加费用
    attribute_cost = 0
    for attr_id in selected_attributes:
        attribute = Attribute.query.get(attr_id)
        if attribute.price_formula:
            # 公式计算
            cost = eval_formula(attribute.price_formula, quantity)
        else:
            # 固定价格
            cost = attribute.price_modifier
        attribute_cost += cost

    # 3. 应用数量折扣
    discount_rate = get_quantity_discount(product, quantity)

    # 4. 计算最终价格
    unit_price = (base_price + attribute_cost) * (1 - discount_rate)
    total_price = unit_price * quantity
```

#### 公式计算支持
- **变量**: quantity (数量)
- **运算符**: +, -, *, /, ()
- **示例**: `quantity * 0.24`, `(quantity - 10) * 0.5`

### 3. 属性系统架构

#### 三级结构
```
分类 (Category)
├── 属性组 (AttributeGroup)
│   ├── 属性1 (Attribute)
│   ├── 属性2 (Attribute)
│   └── 属性3 (Attribute)
└── 属性组2 (AttributeGroup)
    ├── 属性4 (Attribute)
    └── 属性5 (Attribute)
```

#### 属性类型
- **固定价格**: 直接加减固定金额
- **百分比**: 按基础价格百分比计算
- **公式计算**: 支持复杂的数学公式
- **数量相关**: 根据用户输入数量计算

### 4. 文件上传系统

#### 上传配置
```python
UPLOAD_FOLDER = 'static/uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
```

#### 安全措施
- 文件类型验证
- 文件大小限制
- 文件名安全处理
- 存储路径隔离

### 5. 日志系统

#### AdminLogger类
```python
class AdminLogger:
    def log_admin_action(self, action, details=None):
        """记录管理员操作"""
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'user_id': current_user.id,
            'username': current_user.username,
            'action': action,
            'details': details,
            'ip_address': request.remote_addr,
            'user_agent': request.user_agent.string
        }
        # 写入日志文件
```

#### 日志类型
- 管理员操作日志
- 错误日志
- 支付日志
- 系统访问日志

## 性能优化

### 1. 数据库优化
- **索引策略**: 主要查询字段添加索引
- **分页查询**: 大数据量分页显示
- **查询优化**: 避免N+1查询问题
- **连接池**: 数据库连接复用

### 2. 前端优化
- **静态资源**: CSS/JS文件压缩
- **图片优化**: WebP格式支持
- **缓存策略**: 浏览器缓存配置
- **CDN加速**: 静态资源CDN分发

### 3. 缓存策略
- **页面缓存**: 静态页面缓存
- **数据缓存**: 热点数据Redis缓存
- **查询缓存**: 复杂查询结果缓存

## 监控与维护

### 1. 系统监控
- **应用状态**: 服务运行状态监控
- **性能指标**: 响应时间、吞吐量
- **错误监控**: 异常日志收集
- **资源监控**: CPU、内存、磁盘使用率

### 2. 数据备份
- **数据库备份**: 定期全量+增量备份
- **文件备份**: 上传文件定期备份
- **配置备份**: 系统配置文件备份

### 3. 安全维护
- **漏洞扫描**: 定期安全漏洞检查
- **依赖更新**: 第三方包安全更新
- **访问审计**: 异常访问行为监控

## 扩展功能

### 已实现
- ✅ 现代化UI设计
- ✅ 响应式布局
- ✅ 动态价格计算
- ✅ 支付系统集成
- ✅ 完整的管理后台
- ✅ 文件上传管理
- ✅ 用户权限控制
- ✅ 订单状态管理

### 待扩展
- 🔄 购物车完整功能
- 🔄 优惠券系统
- 🔄 库存管理
- 🔄 物流跟踪
- 🔄 客服系统
- 🔄 数据统计分析
- 🔄 短信通知
- 🔄 邮件营销
- 🔄 会员等级系统
- 🔄 积分奖励系统

## 开发规范

### 1. 代码规范
- **PEP 8**: Python代码风格规范
- **命名规范**: 变量、函数、类命名规则
- **注释规范**: 函数、类文档字符串
- **模块化**: 功能模块化组织

### 2. 数据库规范
- **命名规范**: 表名、字段名规范
- **索引规范**: 索引创建和命名
- **约束规范**: 外键、唯一约束设置
- **迁移规范**: 数据库变更管理

### 3. API规范
- **RESTful**: API设计遵循REST原则
- **状态码**: HTTP状态码正确使用
- **响应格式**: 统一的JSON响应格式
- **错误处理**: 统一的错误响应格式

## 部署指南

### 1. 环境要求
- **Python**: 3.8+
- **MySQL**: 5.7+
- **Redis**: 6.0+ (可选)
- **Nginx**: 1.18+ (生产环境)

### 2. 部署步骤
1. **环境准备**: 安装Python、MySQL等依赖
2. **代码部署**: 克隆代码到服务器
3. **依赖安装**: pip install -r requirements.txt
4. **数据库初始化**: 执行SQL脚本
5. **配置文件**: 设置环境变量
6. **启动服务**: 使用Gunicorn启动应用
7. **反向代理**: 配置Nginx反向代理

### 3. 生产配置
```bash
# Gunicorn配置
gunicorn -w 4 -b 0.0.0.0:5000 app:app

# Nginx配置
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /static {
        alias /path/to/static;
        expires 30d;
    }
}
```

## 总结

本系统是一个功能完整的印刷品电商平台，具备现代化的用户界面、灵活的商品管理、完善的订单流程和安全的支付系统。系统采用模块化设计，易于维护和扩展，适合中小型印刷企业的在线销售需求。
