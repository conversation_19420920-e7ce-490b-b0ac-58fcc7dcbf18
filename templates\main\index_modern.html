{% extends "base_modern.html" %}

{% block title %}{{ site_config.site_name }} - 专业印刷品在线商城{% endblock %}
{% block description %}专业印刷服务商城，提供高质量纸箱包装、宣传册印刷等定制化服务{% endblock %}

{% block extra_css %}
<style>
    /* ========== Hero Section ========== */
    .hero-section {
        min-height: 100vh;
        background: var(--hero-gradient);
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
    }
    
    .hero-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
    }
    
    .hero-shape {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        -webkit-backdrop-filter: blur(30px);
    }
    
    .hero-shape-1 {
        width: 600px;
        height: 600px;
        top: -200px;
        right: -200px;
        animation: float 6s ease-in-out infinite;
    }
    
    .hero-shape-2 {
        width: 400px;
        height: 400px;
        bottom: -100px;
        left: -100px;
        animation: float 8s ease-in-out infinite reverse;
    }
    
    .hero-shape-3 {
        width: 300px;
        height: 300px;
        top: 30%;
        right: 10%;
        animation: float 7s ease-in-out infinite;
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
        color: white;
    }
    
    .hero-title {
        font-size: 4rem;
        font-weight: 800;
        line-height: 1.1;
        margin-bottom: 2rem;
        background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: slideInUp 1s ease-out;
    }
    
    .hero-subtitle {
        font-size: 1.5rem;
        font-weight: 300;
        margin-bottom: 3rem;
        opacity: 0.9;
        animation: slideInUp 1s ease-out 0.2s both;
    }
    
    .hero-cta {
        animation: slideInUp 1s ease-out 0.4s both;
    }
    
    .cta-button {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 1rem 2.5rem;
        border-radius: var(--border-radius-xl);
        font-weight: 600;
        font-size: 1.125rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        transition: var(--transition-base);
        margin-right: 1rem;
        margin-bottom: 1rem;
    }
    
    .cta-button:hover {
        background: rgba(255, 255, 255, 0.3);
        color: white;
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }
    
    .cta-button.secondary {
        background: transparent;
        border: 2px solid rgba(255, 255, 255, 0.5);
    }
    
    .cta-button.secondary:hover {
        background: rgba(255, 255, 255, 0.1);
    }
    
    /* ========== Features Section ========== */
    .features-section {
        padding: 8rem 0;
        background: var(--gradient-light);
        position: relative;
    }
    
    .features-container {
        position: relative;
        z-index: 2;
    }
    
    .section-title {
        text-align: center;
        margin-bottom: 6rem;
    }
    
    .section-title h2 {
        font-size: 3rem;
        font-weight: 700;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 1rem;
    }
    
    .section-title p {
        font-size: 1.25rem;
        color: var(--gray-600);
        max-width: 600px;
        margin: 0 auto;
    }
    
    .feature-card {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius-2xl);
        padding: 3rem 2rem;
        text-align: center;
        height: 100%;
        transition: var(--transition-base);
        position: relative;
        overflow: hidden;
    }
    
    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--primary-gradient);
        opacity: 0;
        transition: var(--transition-base);
        z-index: -1;
    }
    
    .feature-card:hover {
        transform: translateY(-15px);
        box-shadow: var(--shadow-xl);
        color: white;
    }
    
    .feature-card:hover::before {
        opacity: 1;
    }
    
    .feature-card:hover .feature-icon {
        color: white;
        transform: scale(1.2);
    }
    
    .feature-card:hover .feature-title,
    .feature-card:hover .feature-text {
        color: white;
    }
    
    .feature-icon {
        font-size: 4rem;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 2rem;
        transition: var(--transition-base);
        display: inline-block;
    }
    
    .feature-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--gray-900);
        transition: var(--transition-base);
    }
    
    .feature-text {
        color: var(--gray-600);
        line-height: 1.6;
        transition: var(--transition-base);
    }
    
    /* 服务联系信息样式 */
    .service-contact {
        border-top: 1px solid rgba(102, 126, 234, 0.2);
        padding-top: 1rem;
    }
    
    .contact-detail {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
    }
    
    .service-phone,
    .service-email {
        color: var(--gray-700);
        text-decoration: none;
        font-weight: 600;
        transition: var(--transition-base);
    }
    
    .service-phone:hover,
    .service-email:hover {
        color: #667eea;
        text-decoration: underline;
    }
    
    .feature-card:hover .service-phone,
    .feature-card:hover .service-email {
        color: white;
    }
    
    .feature-card:hover .service-phone:hover,
    .feature-card:hover .service-email:hover {
        color: #4facfe;
        text-decoration: underline;
    }
    
    /* ========== Products Showcase ========== */
    .products-showcase {
        padding: 8rem 0;
        background: white;
        position: relative;
    }
    
    .product-preview-card {
        background: white;
        border-radius: var(--border-radius-xl);
        overflow: hidden;
        box-shadow: var(--shadow-md);
        transition: var(--transition-base);
        height: 100%;
    }
    
    .product-preview-card:hover {
        transform: translateY(-10px);
        box-shadow: var(--shadow-xl);
    }
    
    .product-preview-image {
        height: 250px;
        background: var(--gradient-light);
        position: relative;
        overflow: hidden;
    }
    
    .product-preview-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: var(--transition-slow);
    }
    
    .product-preview-card:hover .product-preview-image img {
        transform: scale(1.1);
    }
    
    .product-preview-content {
        padding: 2rem;
    }
    
    .product-preview-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0.75rem;
        color: var(--gray-900);
    }
    
    .product-preview-desc {
        color: var(--gray-600);
        margin-bottom: 1.5rem;
        line-height: 1.6;
    }
    
    .product-preview-price {
        font-size: 1.5rem;
        font-weight: 700;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    /* ========== CTA Section ========== */
    .cta-section {
        padding: 8rem 0;
        background: var(--primary-gradient);
        color: white;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .cta-content {
        position: relative;
        z-index: 2;
    }
    
    .cta-title {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
    }
    
    .cta-subtitle {
        font-size: 1.25rem;
        margin-bottom: 3rem;
        opacity: 0.9;
    }
    
    /* 联系信息样式 */
    .contact-info-section {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: var(--border-radius-xl);
        padding: 2rem;
        margin-bottom: 3rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .contact-item {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
        gap: 0.5rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: var(--border-radius-lg);
        transition: var(--transition-base);
        text-align: center;
        min-height: 80px;
    }
    
    .contact-item:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
    }
    
    .contact-item i {
        font-size: 1.25rem;
        color: rgba(255, 255, 255, 0.9);
    }
    
    .contact-label {
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        font-size: 0.9rem;
    }
    
    .contact-value {
        color: white;
        text-decoration: none;
        font-weight: 700;
        font-size: 1rem;
    }
    
    .contact-value:hover {
        color: #4facfe;
        text-decoration: underline;
    }
    
    a.contact-value {
        transition: var(--transition-base);
    }
    
    /* ========== Animations ========== */
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(50px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes slideInLeft {
        from {
            opacity: 0;
            transform: translateX(-50px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(50px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    /* ========== Responsive Design ========== */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }
        
        .hero-subtitle {
            font-size: 1.125rem;
        }
        
        .section-title h2 {
            font-size: 2rem;
        }
        
        .cta-title {
            font-size: 2rem;
        }
        
        .feature-card {
            padding: 2rem 1.5rem;
            margin-bottom: 2rem;
        }
        
        .hero-shape-1,
        .hero-shape-2,
        .hero-shape-3 {
            opacity: 0.5;
        }
        
        /* 移动端联系信息样式 */
        .contact-info-section {
            padding: 1.5rem;
        }
        
        .contact-item {
            padding: 0.75rem;
            min-height: 60px;
            flex-direction: column;
            gap: 0.25rem;
        }
        
        .contact-label {
            font-size: 0.8rem;
        }
        
        .contact-value {
            font-size: 0.9rem;
        }
        
        .service-contact {
            padding-top: 0.75rem;
        }
        
        .contact-detail {
            font-size: 0.8rem;
        }
    }
    
    /* ========== Scroll Animations ========== */
    .animate-on-scroll {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.8s ease-out;
    }
    
    .animate-on-scroll.animated {
        opacity: 1;
        transform: translateY(0);
    }
    
    .animate-on-scroll.delay-1 {
        transition-delay: 0.1s;
    }
    
    .animate-on-scroll.delay-2 {
        transition-delay: 0.2s;
    }
    
    .animate-on-scroll.delay-3 {
        transition-delay: 0.3s;
    }
    
    .animate-on-scroll.delay-4 {
        transition-delay: 0.4s;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-background">
        <div class="hero-shape hero-shape-1"></div>
        <div class="hero-shape hero-shape-2"></div>
        <div class="hero-shape hero-shape-3"></div>
    </div>
    
    <div class="container">
        <div class="row align-items-center min-vh-100">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="hero-title">
                        专业印刷<br>
                        <span style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">精品定制</span>
                    </h1>
                    <p class="hero-subtitle">
                        高质量印刷服务，支持个性化定制，快速交付，满足您的各种印刷需求
                    </p>
                    <div class="hero-cta">
                        <a href="{{ url_for('main.products') }}" class="cta-button">
                            <i class="fas fa-shopping-bag"></i>
                            立即选购
                        </a>
                        <a href="#features" class="cta-button secondary">
                            <i class="fas fa-info-circle"></i>
                            了解更多
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-visual" style="text-align: center; opacity: 0.8;">
                    <i class="fas fa-print" style="font-size: 20rem; color: rgba(255,255,255,0.2);"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section id="features" class="features-section">
    <div class="container features-container">
        <div class="section-title animate-on-scroll">
            <h2>为什么选择我们</h2>
            <p>专业、高效、品质保证的印刷服务，让您的品牌更加出众</p>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="feature-card animate-on-scroll delay-1">
                    <div class="feature-icon">
                        <i class="fas fa-shipping-fast"></i>
                    </div>
                    <h3 class="feature-title">快速发货</h3>
                    <p class="feature-text">24小时内完成生产，48小时内发货，全国包邮服务，让您的项目按时完成</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="feature-card animate-on-scroll delay-2">
                    <div class="feature-icon">
                        <i class="fas fa-award"></i>
                    </div>
                    <h3 class="feature-title">品质保证</h3>
                    <p class="feature-text">严格的质量控制体系，专业的印刷设备，确保每一件产品都达到最高标准</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="feature-card animate-on-scroll delay-3">
                    <div class="feature-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3 class="feature-title">个性定制</h3>
                    <p class="feature-text">支持个性化设计定制，多种规格尺寸可选，满足不同行业的特殊需求</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="feature-card animate-on-scroll delay-4">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3 class="feature-title">专业客服</h3>
                    <p class="feature-text">7×24小时在线客服支持，专业技术团队为您提供一对一服务咨询</p>

                    <!-- 联系方式 -->
                    <div class="service-contact mt-3">
                        <div class="contact-detail">
                            <i class="fas fa-phone me-1"></i>
                            <a href="tel:{{ site_config.company_phone }}" class="service-phone">{{ site_config.company_phone }}</a>
        </div>
                        <div class="contact-detail mt-2">
                            <i class="fas fa-envelope me-1"></i>
                            <a href="mailto:{{ site_config.contact_email }}" class="service-email">{{ site_config.contact_email }}</a>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <div class="cta-content animate-on-scroll">
            <h2 class="cta-title">准备开始您的印刷项目？</h2>
            <p class="cta-subtitle">立即联系我们，获取专业的印刷解决方案和报价</p>
            
            <!-- 联系信息展示 -->
            <div class="contact-info-section mb-4">
                <div class="row justify-content-center g-3">
                    <div class="col-md-4">
                        <div class="contact-item">
                            <i class="fas fa-phone-alt me-2"></i>
                            <span class="contact-label">客服热线：</span>
                            <a href="tel:{{ site_config.company_phone }}" class="contact-value">{{ site_config.company_phone }}</a>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="contact-item">
                            <i class="fas fa-envelope me-2"></i>
                            <span class="contact-label">邮箱：</span>
                            <a href="mailto:{{ site_config.contact_email }}" class="contact-value">{{ site_config.contact_email }}</a>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            <span class="contact-label">地址：</span>
                            <span class="contact-value">{{ site_config.company_address }}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-center gap-3 flex-wrap">
                <a href="{{ url_for('main.products') }}" class="cta-button">
                    <i class="fas fa-shopping-cart"></i>
                    立即购买
                </a>
                <a href="tel:{{ site_config.company_phone }}" class="cta-button secondary">
                    <i class="fas fa-phone"></i>
                    联系客服
                </a>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize scroll animations
    document.addEventListener('DOMContentLoaded', function() {
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Scroll reveal animation
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);
        
        // Observe all animate-on-scroll elements
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
        
        // Hero typing effect (optional enhancement)
        const heroTitle = document.querySelector('.hero-title');
        if (heroTitle) {
            heroTitle.style.opacity = '0';
            setTimeout(() => {
                heroTitle.style.opacity = '1';
                heroTitle.style.animation = 'slideInUp 1s ease-out';
            }, 300);
        }
        
        // Parallax effect for hero shapes
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const parallax1 = document.querySelector('.hero-shape-1');
            const parallax2 = document.querySelector('.hero-shape-2');
            const parallax3 = document.querySelector('.hero-shape-3');
            
            if (parallax1) {
                parallax1.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
            if (parallax2) {
                parallax2.style.transform = `translateY(${scrolled * -0.3}px)`;
            }
            if (parallax3) {
                parallax3.style.transform = `translateY(${scrolled * 0.2}px)`;
            }
        });
    });
</script>
{% endblock %} 