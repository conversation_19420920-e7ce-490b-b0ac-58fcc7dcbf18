#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查attributes表的结构
"""

import sys
import os
import pymysql
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('check')

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from config import Config

def check_mysql_table():
    """检查MySQL数据库中的attributes表结构"""
    try:
        # 从Config获取MySQL配置
        mysql_config = {
            'host': Config.MYSQL_HOST,
            'user': Config.MYSQL_USER,
            'password': Config.MYSQL_PASSWORD,
            'db': Config.MYSQL_DB,
            'charset': 'utf8mb4'
        }
        
        # 连接MySQL数据库
        conn = pymysql.connect(**mysql_config)
        logger.info("已连接到MySQL数据库")
        
        cursor = conn.cursor()
        
        # 获取表结构
        cursor.execute("DESCRIBE attributes")
        columns = cursor.fetchall()
        
        logger.info("=== attributes表结构 ===")
        for column in columns:
            logger.info(f"列名: {column[0]}, 类型: {column[1]}, 是否可空: {column[2]}, 键: {column[3]}, 默认值: {column[4]}, 额外: {column[5]}")
        
        # 检查具体的列是否存在
        formula_fields = ['price_formula', 'is_quantity_based', 'quantity_unit', 'min_quantity', 'max_quantity']
        for field in formula_fields:
            cursor.execute(f"SHOW COLUMNS FROM attributes LIKE '{field}'")
            if cursor.fetchone():
                logger.info(f"✓ 列 '{field}' 已存在")
            else:
                logger.error(f"✗ 列 '{field}' 不存在")
        
        # 检查示例数据
        cursor.execute("SELECT id, name, value, price_formula, is_quantity_based, quantity_unit FROM attributes WHERE is_quantity_based = 1 LIMIT 5")
        rows = cursor.fetchall()
        
        if rows:
            logger.info("=== 支持数量输入的属性示例 ===")
            for row in rows:
                logger.info(f"ID: {row[0]}, 名称: {row[1]}, 值: {row[2]}, 公式: {row[3]}, 支持数量: {row[4]}, 单位: {row[5]}")
        else:
            logger.info("没有找到支持数量输入的属性")
        
        cursor.close()
        conn.close()
        
        return True
    except Exception as e:
        logger.error(f"MySQL检查失败: {e}")
        return False

def check_sqlite_table():
    """检查SQLite数据库中的attributes表结构"""
    try:
        # 创建Flask应用获取SQLite连接
        app = create_app()
        
        with app.app_context():
            from models import db
            conn = db.engine.raw_connection()
            logger.info("已连接到SQLite数据库")
            
            cursor = conn.cursor()
            
            # 获取表结构
            cursor.execute("PRAGMA table_info(attributes)")
            columns = cursor.fetchall()
            
            logger.info("=== attributes表结构 ===")
            for column in columns:
                logger.info(f"ID: {column[0]}, 列名: {column[1]}, 类型: {column[2]}, 是否可空: {'否' if column[3] else '是'}, 默认值: {column[4]}, 主键: {'是' if column[5] else '否'}")
            
            # 检查具体的列是否存在
            formula_fields = ['price_formula', 'is_quantity_based', 'quantity_unit', 'min_quantity', 'max_quantity']
            for field in formula_fields:
                found = False
                for column in columns:
                    if column[1] == field:
                        found = True
                        logger.info(f"✓ 列 '{field}' 已存在")
                        break
                
                if not found:
                    logger.error(f"✗ 列 '{field}' 不存在")
            
            # 检查示例数据
            cursor.execute("SELECT id, name, value, price_formula, is_quantity_based, quantity_unit FROM attributes WHERE is_quantity_based = 1 LIMIT 5")
            rows = cursor.fetchall()
            
            if rows:
                logger.info("=== 支持数量输入的属性示例 ===")
                for row in rows:
                    logger.info(f"ID: {row[0]}, 名称: {row[1]}, 值: {row[2]}, 公式: {row[3]}, 支持数量: {row[4]}, 单位: {row[5]}")
            else:
                logger.info("没有找到支持数量输入的属性")
            
            cursor.close()
            conn.close()
            
            return True
    except Exception as e:
        logger.error(f"SQLite检查失败: {e}")
        return False

def check_table():
    """检查数据库表结构，自动检测数据库类型"""
    # 检查是否使用MySQL
    if hasattr(Config, 'MYSQL_HOST') and Config.MYSQL_HOST:
        logger.info("检测到MySQL配置，检查MySQL数据库表结构")
        return check_mysql_table()
    else:
        logger.info("未检测到MySQL配置，检查SQLite数据库表结构")
        return check_sqlite_table()

if __name__ == '__main__':
    logger.info("开始检查数据库表结构...")
    success = check_table()
    
    if success:
        logger.info("✅ 数据库表结构检查完成！")
        sys.exit(0)
    else:
        logger.error("❌ 数据库表结构检查失败！")
        sys.exit(1) 