{% extends "admin/base.html" %}

{% block title %}{{ title }} - 管理后台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ title }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('admin.attribute_groups') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回列表
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}

                    <div class="mb-3">
                        {{ form.category_id.label(class="form-label") }}
                        {{ form.category_id(class="form-select") }}
                        {% if form.category_id.errors %}
                        <div class="text-danger small">
                            {% for error in form.category_id.errors %}
                            <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}
                        <div class="form-text">选择该大属性组所属的分类</div>
                    </div>

                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control") }}
                        {% if form.name.errors %}
                        <div class="text-danger small">
                            {% for error in form.name.errors %}
                            <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}
                        <div class="form-text">大属性的名称，如"纸张类型"、"印刷工艺"、"尺寸规格"等</div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control", rows="3") }}
                        {% if form.description.errors %}
                        <div class="text-danger small">
                            {% for error in form.description.errors %}
                            <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}
                        <div class="form-text">属性组的详细描述（可选）</div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.display_type.label(class="form-label") }}
                        {{ form.display_type(class="form-select") }}
                        {% if form.display_type.errors %}
                        <div class="text-danger small">
                            {% for error in form.display_type.errors %}
                            <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}
                        <div class="form-text">
                            <strong>单选按钮</strong>：适合选项较少（2-5个）的属性，如颜色、材质等<br>
                            <strong>下拉框</strong>：适合选项较多（5个以上）的属性，如尺寸规格、联数等
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.sort_order.label(class="form-label") }}
                                {{ form.sort_order(class="form-control") }}
                                {% if form.sort_order.errors %}
                                <div class="text-danger small">
                                    {% for error in form.sort_order.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">数字越小排序越靠前</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">状态</label>
                                <div class="form-check">
                                    {{ form.is_active(class="form-check-input") }}
                                    {{ form.is_active.label(class="form-check-label") }}
                                </div>
                                {% if form.is_active.errors %}
                                <div class="text-danger small">
                                    {% for error in form.is_active.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('admin.attribute_groups') }}" class="btn btn-secondary">取消</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>帮助信息
                </h6>
            </div>
            <div class="card-body">
                <h6>属性组说明</h6>
                <ul class="small">
                    <li><strong>属性组</strong>是属性的分类容器</li>
                    <li>每个属性组可以包含多个具体属性</li>
                    <li>属性组用于在商品页面中组织和展示属性</li>
                    <li>排序值决定属性组在页面中的显示顺序</li>
                </ul>
                
                <h6 class="mt-3">常见属性组示例</h6>
                <ul class="small">
                    <li><strong>颜色</strong>：红色、蓝色、绿色等</li>
                    <li><strong>尺寸</strong>：小号、中号、大号等</li>
                    <li><strong>材质</strong>：棉质、丝质、麻质等</li>
                    <li><strong>规格</strong>：A4、A3、A5等</li>
                </ul>
                
                <div class="alert alert-info small mt-3">
                    <i class="fas fa-lightbulb me-1"></i>
                    建议先创建属性组，再在属性组下添加具体的属性值。
                </div>
            </div>
        </div>
        
        {% if group %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>当前属性组信息
                </h6>
            </div>
            <div class="card-body">
                <p><strong>属性组名称</strong>：{{ group.name }}</p>
                <p><strong>属性数量</strong>：{{ group.get_attributes_count() }}</p>
                <p><strong>排序值</strong>：{{ group.sort_order }}</p>
                <p><strong>状态</strong>：
                    <span class="badge bg-{{ 'success' if group.is_active else 'secondary' }}">
                        {{ '启用' if group.is_active else '禁用' }}
                    </span>
                </p>
                <p><strong>创建时间</strong>：{{ group.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                
                {% if group.get_attributes_count() > 0 %}
                <div class="mt-3">
                    <a href="{{ url_for('admin.attributes', group_id=group.id) }}" class="btn btn-sm btn-outline-info">
                        <i class="fas fa-eye me-1"></i>查看属性
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
