-- 更新系统配置表结构
-- 添加新字段
ALTER TABLE system_configs ADD COLUMN IF NOT EXISTS group_name VARCHAR(50) DEFAULT '其他';
ALTER TABLE system_configs ADD COLUMN IF NOT EXISTS config_type VARCHAR(20) DEFAULT 'text';
ALTER TABLE system_configs ADD COLUMN IF NOT EXISTS is_required BOOLEAN DEFAULT FALSE;
ALTER TABLE system_configs ADD COLUMN IF NOT EXISTS validation_rules JSON;

-- 更新现有配置项的分组和类型
UPDATE system_configs SET 
    group_name = '基本信息',
    config_type = 'text',
    is_required = TRUE
WHERE config_key = 'site_name';

UPDATE system_configs SET 
    group_name = '基本信息',
    config_type = 'text',
    is_required = TRUE
WHERE config_key = 'company_name';

UPDATE system_configs SET 
    group_name = '基本信息',
    config_type = 'text',
    is_required = TRUE
WHERE config_key = 'company_phone';

UPDATE system_configs SET 
    group_name = '基本信息',
    config_type = 'email',
    is_required = TRUE
WHERE config_key = 'company_email';

UPDATE system_configs SET 
    group_name = '运费设置',
    config_type = 'number',
    is_required = TRUE,
    validation_rules = '{"min": 0}'
WHERE config_key = 'free_shipping_amount';

UPDATE system_configs SET 
    group_name = '运费设置',
    config_type = 'number',
    is_required = TRUE,
    validation_rules = '{"min": 0}'
WHERE config_key = 'default_shipping_fee';

UPDATE system_configs SET 
    group_name = '系统限制',
    config_type = 'number',
    is_required = TRUE,
    validation_rules = '{"min": 1, "max": 999}'
WHERE config_key = 'max_cart_items';

UPDATE system_configs SET 
    group_name = '订单设置',
    config_type = 'number',
    is_required = TRUE,
    validation_rules = '{"min": 1, "max": 30}'
WHERE config_key = 'order_auto_confirm_days';

-- 插入缺失的配置项
INSERT INTO system_configs (config_key, config_value, description, group_name, config_type, is_required, validation_rules)
VALUES 
    ('company_address', '', '公司地址', '基本信息', 'text', FALSE, NULL),
    ('min_order_amount', '50', '最小订单金额', '订单设置', 'number', FALSE, '{"min": 0}'),
    ('session_timeout', '3600', '会话超时时间(秒)', '系统限制', 'number', FALSE, '{"min": 300, "max": 86400}')
ON DUPLICATE KEY UPDATE 
    group_name = VALUES(group_name),
    config_type = VALUES(config_type),
    is_required = VALUES(is_required),
    validation_rules = VALUES(validation_rules); 