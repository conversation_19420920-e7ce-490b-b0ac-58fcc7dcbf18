# API接口文档

## 接口概述

### 基础信息
- **基础URL**: `http://127.0.0.1:5000`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: Session + CSRF Token

### 通用响应格式
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "error": null
}
```

### 错误响应格式
```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

## 前台API接口

### 1. 价格计算API

#### 接口信息
- **URL**: `/api/calculate-price`
- **方法**: POST
- **权限**: 无需登录
- **CSRF**: 已免除

#### 请求参数
```json
{
  "product_id": 1,
  "quantity": 10,
  "selected_attributes": [1, 2, 3],
  "attributes": {
    "1": "选项1",
    "2": "选项2"
  },
  "attribute_quantities": {
    "1": 50,
    "2": 100
  }
}
```

#### 参数说明
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| product_id | int | 是 | 商品ID |
| quantity | int | 否 | 商品数量，默认1 |
| selected_attributes | array | 否 | 选择的属性ID列表 |
| attributes | object | 否 | 属性键值对 |
| attribute_quantities | object | 否 | 属性自定义数量 |

#### 响应示例
```json
{
  "unit_price": 12.50,
  "total_price": 125.00,
  "discount_rate": 0.1,
  "breakdown": {
    "base_price": 10.00,
    "attribute_cost": 2.50,
    "quantity_discount": -1.25
  },
  "discount_info": {
    "applied": true,
    "min_quantity": 10,
    "discount_rate": 0.1
  }
}
```

#### 错误响应
```json
{
  "error": "商品不存在"
}
```

### 2. 订单管理API

#### 2.1 取消订单

##### 接口信息
- **URL**: `/api/orders/<int:id>/cancel`
- **方法**: POST
- **权限**: 登录用户，订单所有者

##### 请求参数
无需请求体

##### 响应示例
```json
{
  "success": true,
  "message": "订单已成功取消"
}
```

##### 错误响应
```json
{
  "success": false,
  "message": "订单状态为\"已支付\"，无法取消"
}
```

#### 2.2 发起支付

##### 接口信息
- **URL**: `/api/orders/<int:id>/pay`
- **方法**: POST
- **权限**: 登录用户，订单所有者

##### 请求参数
```json
{
  "payment_type": "alipay"
}
```

##### 参数说明
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| payment_type | string | 是 | 支付方式：alipay, wxpay |

##### 响应示例
```json
{
  "success": true,
  "payment_url": "https://z-pay.cn/submit.php?money=125.00&name=订单支付&...",
  "order_no": "ORDER20241215001",
  "amount": 125.00
}
```

##### 错误响应
```json
{
  "success": false,
  "message": "不支持的支付方式: qqpay"
}
```

#### 2.3 同步支付状态

##### 接口信息
- **URL**: `/api/orders/<int:id>/sync-payment`
- **方法**: POST
- **权限**: 登录用户，订单所有者

##### 请求参数
无需请求体

##### 响应示例
```json
{
  "success": true,
  "message": "支付状态已更新",
  "payment_status": "paid",
  "order_status": "paid"
}
```

## 管理后台API接口

### 1. 分类管理API

#### 1.1 删除分类

##### 接口信息
- **URL**: `/admin/api/categories/<int:id>/delete`
- **方法**: DELETE
- **权限**: 管理员

##### 响应示例
```json
{
  "success": true,
  "message": "分类删除成功"
}
```

##### 错误响应
```json
{
  "success": false,
  "message": "该分类下还有商品，无法删除"
}
```

#### 1.2 获取分类属性

##### 接口信息
- **URL**: `/admin/api/attributes-by-category/<int:category_id>`
- **方法**: GET
- **权限**: 管理员

##### 响应示例
```json
{
  "success": true,
  "data": [
    {
      "group_name": "材质",
      "group_id": 1,
      "attributes": [
        {
          "id": 1,
          "name": "牛皮纸",
          "value": "kraft_paper",
          "price_modifier": 0.00
        },
        {
          "id": 2,
          "name": "瓦楞纸",
          "value": "corrugated_paper",
          "price_modifier": 2.50
        }
      ]
    }
  ]
}
```

### 2. 商品管理API

#### 2.1 删除商品

##### 接口信息
- **URL**: `/admin/api/products/<int:id>/delete`
- **方法**: DELETE
- **权限**: 管理员

##### 响应示例
```json
{
  "success": true,
  "message": "商品删除成功"
}
```

### 3. 订单管理API

#### 3.1 更新订单状态

##### 接口信息
- **URL**: `/admin/api/orders/<int:id>/update-status`
- **方法**: POST
- **权限**: 管理员

##### 请求参数
```json
{
  "status": "processing"
}
```

##### 参数说明
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| status | string | 是 | 订单状态：pending, paid, processing, shipped, delivered, cancelled |

##### 响应示例
```json
{
  "success": true,
  "message": "订单状态已更新",
  "old_status": "paid",
  "new_status": "processing"
}
```

## 支付回调接口

### 1. 支付通知

#### 接口信息
- **URL**: `/payment/notify`
- **方法**: POST
- **权限**: 支付网关
- **说明**: 由支付网关调用，用于通知支付结果

#### 请求参数
```json
{
  "money": "125.00",
  "name": "订单支付",
  "out_trade_no": "ORDER20241215001",
  "pid": "2025053120440356",
  "trade_no": "2024121522001234567890",
  "trade_status": "TRADE_SUCCESS",
  "type": "alipay",
  "sign": "generated_signature",
  "sign_type": "MD5"
}
```

#### 响应格式
- **成功**: 返回 "success"
- **失败**: 返回 "fail"

## 错误码说明

### HTTP状态码
| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

### 业务错误码
| 错误码 | 说明 |
|--------|------|
| 1001 | 商品不存在 |
| 1002 | 商品已下架 |
| 1003 | 库存不足 |
| 2001 | 订单不存在 |
| 2002 | 订单状态错误 |
| 2003 | 订单权限不足 |
| 3001 | 支付方式不支持 |
| 3002 | 支付金额错误 |
| 3003 | 支付签名验证失败 |

## 接口调用示例

### JavaScript调用示例

#### 价格计算
```javascript
fetch('/api/calculate-price', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        product_id: 1,
        quantity: 10,
        selected_attributes: [1, 2, 3]
    })
})
.then(response => response.json())
.then(data => {
    if (data.error) {
        console.error('计算失败:', data.error);
    } else {
        console.log('单价:', data.unit_price);
        console.log('总价:', data.total_price);
    }
});
```

#### 订单支付
```javascript
fetch(`/api/orders/${orderId}/pay`, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({
        payment_type: 'alipay'
    })
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        window.location.href = data.payment_url;
    } else {
        alert(data.message);
    }
});
```

### Python调用示例

#### 使用requests库
```python
import requests

# 价格计算
response = requests.post('http://127.0.0.1:5000/api/calculate-price', json={
    'product_id': 1,
    'quantity': 10,
    'selected_attributes': [1, 2, 3]
})

if response.status_code == 200:
    data = response.json()
    if 'error' not in data:
        print(f"单价: {data['unit_price']}")
        print(f"总价: {data['total_price']}")
    else:
        print(f"错误: {data['error']}")
```

## 接口测试

### 测试工具推荐
- **Postman**: 图形化接口测试工具
- **curl**: 命令行测试工具
- **HTTPie**: 现代化命令行HTTP客户端

### curl测试示例
```bash
# 价格计算
curl -X POST http://127.0.0.1:5000/api/calculate-price \
  -H "Content-Type: application/json" \
  -d '{
    "product_id": 1,
    "quantity": 10,
    "selected_attributes": [1, 2, 3]
  }'

# 取消订单（需要登录状态）
curl -X POST http://127.0.0.1:5000/api/orders/1/cancel \
  -H "Content-Type: application/json" \
  -H "X-CSRFToken: your-csrf-token" \
  -b "session=your-session-cookie"
```

## 注意事项

### 1. CSRF保护
- 大部分POST接口需要CSRF Token
- Token通过meta标签或cookie获取
- 价格计算API已免除CSRF保护

### 2. 会话管理
- 登录后会话有效期为30天（如果选择记住登录）
- 会话过期需要重新登录
- 管理员接口需要管理员权限

### 3. 频率限制
- 目前未实施频率限制
- 建议合理调用，避免过于频繁的请求

### 4. 数据格式
- 所有金额以元为单位，保留2位小数
- 时间格式为ISO 8601标准
- 布尔值使用true/false
