/**
 * Modern Components CSS
 * 现代化组件样式库 - 高级UI组件
 * @version 2.0.0
 * <AUTHOR> Programming Master
 */

/* ========== Hero 区域组件 ========== */
.hero {
    position: relative;
    min-height: 80vh;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    overflow: hidden;
    color: white;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%" r="50%"><stop offset="0%" stop-color="rgba(255,255,255,0.1)"/><stop offset="100%" stop-color="rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>') no-repeat center center;
    background-size: cover;
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    padding: var(--space-8);
}

.hero-title {
    font-size: clamp(2rem, 5vw, 4rem);
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    margin-bottom: var(--space-6);
    background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: var(--text-xl);
    font-weight: var(--font-normal);
    opacity: 0.9;
    margin-bottom: var(--space-8);
    line-height: var(--leading-relaxed);
}

.hero-actions {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    flex-wrap: wrap;
}

/* ========== 产品卡片组件 ========== */
.product-card {
    background: white;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition-all);
    position: relative;
    border: 1px solid var(--gray-200);
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
    border-color: var(--primary-200);
}

.product-card-image {
    position: relative;
    overflow: hidden;
    aspect-ratio: 4/3;
    background: var(--gray-100);
}

.product-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-transform);
}

.product-card:hover .product-card-image img {
    transform: scale(1.05);
}

.product-card-badge {
    position: absolute;
    top: var(--space-3);
    right: var(--space-3);
    background: var(--gradient-danger);
    color: white;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.product-card-content {
    padding: var(--space-6);
}

.product-card-title {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--gray-900);
    margin-bottom: var(--space-2);
    line-height: var(--leading-tight);
}

.product-card-description {
    font-size: var(--text-sm);
    color: var(--gray-600);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--space-4);
}

.product-card-price {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-4);
}

.product-card-price-current {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.product-card-price-original {
    font-size: var(--text-sm);
    color: var(--gray-400);
    text-decoration: line-through;
}

.product-card-actions {
    display: flex;
    gap: var(--space-2);
}

/* ========== 购物车组件 ========== */
.cart-item {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    margin-bottom: var(--space-4);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: var(--transition-all);
}

.cart-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.cart-item-content {
    display: grid;
    grid-template-columns: auto 1fr auto auto auto;
    gap: var(--space-4);
    align-items: center;
}

.cart-item-image {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item-details h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.cart-item-specs {
    font-size: var(--text-sm);
    color: var(--gray-500);
    margin-bottom: var(--space-2);
}

.cart-item-price {
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    color: var(--primary-600);
}

.quantity-selector {
    display: flex;
    align-items: center;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: white;
}

.quantity-btn {
    padding: var(--space-2) var(--space-3);
    border: none;
    background: transparent;
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition-colors);
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-btn:hover {
    background: var(--gray-100);
    color: var(--primary-600);
}

.quantity-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.quantity-input {
    border: none;
    padding: var(--space-2);
    text-align: center;
    font-weight: var(--font-medium);
    width: 60px;
    background: transparent;
}

.quantity-input:focus {
    outline: none;
    background: var(--gray-50);
}

/* ========== 导航组件 ========== */
.navbar-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 50;
}

.navbar-brand-modern {
    font-family: var(--font-family-heading);
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-decoration: none;
}

.nav-link-modern {
    position: relative;
    padding: var(--space-2) var(--space-4);
    color: var(--gray-700);
    text-decoration: none;
    font-weight: var(--font-medium);
    transition: var(--transition-colors);
}

.nav-link-modern::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: var(--transition-all);
    transform: translateX(-50%);
}

.nav-link-modern:hover::after,
.nav-link-modern.active::after {
    width: 100%;
}

.nav-link-modern:hover {
    color: var(--primary-600);
}

/* ========== 表单组件 ========== */
.form-floating-modern {
    position: relative;
    margin-bottom: var(--space-6);
}

.form-floating-modern input,
.form-floating-modern textarea {
    width: 100%;
    padding: var(--space-4) var(--space-4) var(--space-4) var(--space-4);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-xl);
    font-size: var(--text-base);
    background: white;
    transition: var(--transition-all);
    outline: none;
}

.form-floating-modern input:focus,
.form-floating-modern textarea:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.form-floating-modern label {
    position: absolute;
    top: var(--space-4);
    left: var(--space-4);
    color: var(--gray-500);
    transition: var(--transition-all);
    pointer-events: none;
    background: white;
    padding: 0 var(--space-2);
}

.form-floating-modern input:focus + label,
.form-floating-modern input:not(:placeholder-shown) + label,
.form-floating-modern textarea:focus + label,
.form-floating-modern textarea:not(:placeholder-shown) + label {
    transform: translateY(-140%) scale(0.875);
    color: var(--primary-600);
    font-weight: var(--font-medium);
}

/* ========== 模态框组件 ========== */
.modal-modern {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-all);
}

.modal-modern.show {
    opacity: 1;
    visibility: visible;
}

.modal-content-modern {
    background: white;
    border-radius: var(--radius-3xl);
    box-shadow: var(--shadow-2xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.8);
    transition: var(--transition-transform);
}

.modal-modern.show .modal-content-modern {
    transform: scale(1);
}

.modal-header-modern {
    padding: var(--space-8) var(--space-8) var(--space-4);
    border-bottom: 1px solid var(--gray-200);
}

.modal-title-modern {
    font-size: var(--text-2xl);
    font-weight: var(--font-semibold);
    color: var(--gray-900);
    margin: 0;
}

.modal-body-modern {
    padding: var(--space-6) var(--space-8);
}

.modal-footer-modern {
    padding: var(--space-4) var(--space-8) var(--space-8);
    display: flex;
    gap: var(--space-4);
    justify-content: flex-end;
}

/* ========== 通知组件 ========== */
.notification-modern {
    position: fixed;
    top: var(--space-8);
    right: var(--space-8);
    max-width: 400px;
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    padding: var(--space-6);
    border-left: 4px solid var(--primary-500);
    transform: translateX(100%);
    transition: var(--transition-transform);
    z-index: 1000;
}

.notification-modern.show {
    transform: translateX(0);
}

.notification-modern.success {
    border-left-color: #10b981;
}

.notification-modern.warning {
    border-left-color: #f59e0b;
}

.notification-modern.error {
    border-left-color: #ef4444;
}

.notification-content {
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
}

.notification-icon {
    font-size: var(--text-xl);
    margin-top: var(--space-1);
}

.notification-icon.success {
    color: #10b981;
}

.notification-icon.warning {
    color: #f59e0b;
}

.notification-icon.error {
    color: #ef4444;
}

.notification-body h4 {
    font-size: var(--text-base);
    font-weight: var(--font-semibold);
    color: var(--gray-900);
    margin: 0 0 var(--space-1) 0;
}

.notification-body p {
    font-size: var(--text-sm);
    color: var(--gray-600);
    margin: 0;
    line-height: var(--leading-relaxed);
}

.notification-close {
    position: absolute;
    top: var(--space-3);
    right: var(--space-3);
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--space-1);
    border-radius: var(--radius);
    transition: var(--transition-colors);
}

.notification-close:hover {
    color: var(--gray-600);
    background: var(--gray-100);
}

/* ========== 加载组件 ========== */
.loading-modern {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-sm);
    color: var(--gray-600);
}

.loading-spinner-modern {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-top-color: var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.loading-dots {
    display: inline-flex;
    gap: var(--space-1);
}

.loading-dot {
    width: 6px;
    height: 6px;
    background: var(--primary-500);
    border-radius: 50%;
    animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* ========== 进度条组件 ========== */
.progress-modern {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
    position: relative;
}

.progress-bar-modern {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    transition: width 0.5s ease;
    position: relative;
}

.progress-bar-modern::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* ========== 标签组件 ========== */
.tag-modern {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.tag-primary {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-700);
}

.tag-success {
    background: rgba(16, 185, 129, 0.1);
    color: #065f46;
}

.tag-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #92400e;
}

.tag-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #991b1b;
}

/* ========== 工具提示组件 ========== */
.tooltip-modern {
    position: relative;
    display: inline-block;
}

.tooltip-modern::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-900);
    color: white;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius);
    font-size: var(--text-xs);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-all);
    z-index: 1000;
    margin-bottom: var(--space-2);
}

.tooltip-modern::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--gray-900);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-all);
}

.tooltip-modern:hover::before,
.tooltip-modern:hover::after {
    opacity: 1;
    visibility: visible;
}

/* ========== 分页组件 ========== */
.pagination-modern {
    display: flex;
    gap: var(--space-2);
    justify-content: center;
    align-items: center;
}

.pagination-item {
    padding: var(--space-2) var(--space-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    color: var(--gray-700);
    text-decoration: none;
    transition: var(--transition-all);
    font-weight: var(--font-medium);
}

.pagination-item:hover {
    background: var(--gray-100);
    border-color: var(--gray-400);
    color: var(--gray-900);
}

.pagination-item.active {
    background: var(--gradient-primary);
    border-color: transparent;
    color: white;
}

.pagination-item:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* ========== 面包屑组件 ========== */
.breadcrumb-modern {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-sm);
    color: var(--gray-600);
    margin-bottom: var(--space-6);
}

.breadcrumb-item {
    color: var(--gray-500);
    text-decoration: none;
    transition: var(--transition-colors);
}

.breadcrumb-item:hover {
    color: var(--primary-600);
}

.breadcrumb-item.active {
    color: var(--gray-900);
    font-weight: var(--font-medium);
}

.breadcrumb-separator {
    color: var(--gray-400);
    margin: 0 var(--space-1);
}

/* ========== 统计卡片组件 ========== */
.stat-card {
    background: white;
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: var(--transition-all);
    text-align: center;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.stat-number {
    font-size: var(--text-4xl);
    font-weight: var(--font-bold);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: var(--leading-none);
    margin-bottom: var(--space-2);
}

.stat-label {
    font-size: var(--text-sm);
    color: var(--gray-600);
    font-weight: var(--font-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-change {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    margin-top: var(--space-2);
}

.stat-change.positive {
    color: #10b981;
}

.stat-change.negative {
    color: #ef4444;
}

/* ========== 响应式优化 ========== */
@media (max-width: 768px) {
    .cart-item-content {
        grid-template-columns: auto 1fr;
        gap: var(--space-3);
    }
    
    .cart-item-image {
        width: 60px;
        height: 60px;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .notification-modern {
        top: var(--space-4);
        right: var(--space-4);
        left: var(--space-4);
        max-width: none;
    }
    
    .modal-content-modern {
        margin: var(--space-4);
        width: calc(100% - var(--space-8));
    }
    
    .stat-card {
        padding: var(--space-6);
    }
    
    .stat-number {
        font-size: var(--text-3xl);
    }
}

/* ========== 打印样式 ========== */
@media print {
    .hero,
    .navbar-modern,
    .modal-modern,
    .notification-modern,
    .fab,
    .tooltip-modern::before,
    .tooltip-modern::after {
        display: none !important;
    }
    
    .product-card,
    .cart-item {
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }
} 