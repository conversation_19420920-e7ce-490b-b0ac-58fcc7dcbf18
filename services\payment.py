#!/usr/bin/env python
# -*- coding: utf-8 -*-

import hashlib
import requests
import json
from datetime import datetime
from urllib.parse import urlencode
from flask import current_app

class PaymentService:
    """易支付服务类，封装所有支付相关操作"""
    
    def __init__(self):
        self.gateway = current_app.config.get('EPAY_GATEWAY')
        self.pid = current_app.config.get('EPAY_PID')
        self.key = current_app.config.get('EPAY_KEY')
        self.submit_url = current_app.config.get('EPAY_SUBMIT_URL')
        self.api_url = current_app.config.get('EPAY_API_URL')
    
    def _generate_sign(self, params):
        """生成签名"""
        # 按照易支付要求的参数顺序构建签名字符串
        sign_str = f"money={params['money']}&name={params['name']}&notify_url={params['notify_url']}&out_trade_no={params['out_trade_no']}&pid={params['pid']}&return_url={params['return_url']}&sitename={params['sitename']}&type={params['type']}"
        
        # MD5加密
        sign = hashlib.md5((sign_str + self.key).encode('utf-8')).hexdigest()
        return sign
    
    def create_payment(self, order_no, amount, product_name, return_url, notify_url, 
                      payment_type='alipay', site_name='印刷品商城'):
        """
        创建支付订单
        
        Args:
            order_no: 商户订单号
            amount: 支付金额（元）
            product_name: 商品名称
            return_url: 页面跳转通知地址
            notify_url: 服务器异步通知地址
            payment_type: 支付方式 (alipay:支付宝, wxpay:微信支付, qqpay:QQ钱包)
            site_name: 网站名称
            
        Returns:
            dict: 包含支付URL和相关信息
        """
        try:
            # 构建支付参数
            params = {
                'money': str(amount),
                'name': product_name,
                'notify_url': notify_url,
                'out_trade_no': order_no,
                'pid': self.pid,
                'return_url': return_url,
                'sitename': site_name,
                'type': payment_type
            }
            
            # 生成签名
            sign = self._generate_sign(params)
            
            # 构建完整的支付URL
            params['sign'] = sign
            params['sign_type'] = 'MD5'
            
            payment_url = f"{self.submit_url}?{urlencode(params)}"
            
            return {
                'success': True,
                'payment_url': payment_url,
                'order_no': order_no,
                'amount': amount
            }
            
        except Exception as e:
            current_app.logger.error(f"创建支付订单失败: {str(e)}")
            return {
                'success': False,
                'error': f"创建支付订单失败: {str(e)}"
            }
    
    def verify_notify(self, params):
        """
        验证支付回调通知
        
        Args:
            params: 回调参数字典
            
        Returns:
            bool: 验证是否成功
        """
        try:
            # 获取回调中的签名
            received_sign = params.get('sign', '')
            
            # 构建验证签名的字符串（排除sign参数）
            verify_params = {k: v for k, v in params.items() if k != 'sign'}
            
            # 按字母顺序排序参数
            sorted_params = sorted(verify_params.items())
            sign_str = '&'.join([f"{k}={v}" for k, v in sorted_params])
            
            # 加上密钥生成签名
            calculated_sign = hashlib.md5((sign_str + self.key).encode('utf-8')).hexdigest()
            
            return calculated_sign.lower() == received_sign.lower()
            
        except Exception as e:
            current_app.logger.error(f"验证支付回调失败: {str(e)}")
            return False
    
    def query_order(self, out_trade_no):
        """
        查询订单状态
        
        Args:
            out_trade_no: 商户订单号
            
        Returns:
            dict: 订单状态信息
        """
        try:
            url = f"{self.api_url}?act=order&pid={self.pid}&key={self.key}&out_trade_no={out_trade_no}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                result = response.text
                # 尝试解析JSON响应
                try:
                    return json.loads(result)
                except json.JSONDecodeError:
                    return {'success': False, 'error': '响应格式错误', 'raw_response': result}
            else:
                return {'success': False, 'error': f'HTTP错误: {response.status_code}'}
                
        except Exception as e:
            current_app.logger.error(f"查询订单状态失败: {str(e)}")
            return {'success': False, 'error': f'查询订单状态失败: {str(e)}'}
    
    def query_merchant_info(self):
        """
        查询商户信息与结算规则
        
        Returns:
            dict: 商户信息
        """
        try:
            url = f"{self.api_url}?act=query&pid={self.pid}&key={self.key}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                result = response.text
                try:
                    return json.loads(result)
                except json.JSONDecodeError:
                    return {'success': False, 'error': '响应格式错误', 'raw_response': result}
            else:
                return {'success': False, 'error': f'HTTP错误: {response.status_code}'}
                
        except Exception as e:
            current_app.logger.error(f"查询商户信息失败: {str(e)}")
            return {'success': False, 'error': f'查询商户信息失败: {str(e)}'}
    
    def get_payment_types(self):
        """
        获取支持的支付方式列表
        
        Returns:
            list: 支付方式列表
        """
        return [
            {
                'code': 'alipay',
                'name': '支付宝',
                'icon': 'fab fa-alipay',
                'color': '#1677ff',
                'enabled': True
            },
            {
                'code': 'wxpay',
                'name': '微信支付',
                'icon': 'fab fa-weixin',
                'color': '#07c160',
                'enabled': True  # 改为启用状态
            }
        ]
    
    @staticmethod
    def format_amount(amount):
        """
        格式化金额显示
        
        Args:
            amount: 金额（元或分）
            
        Returns:
            str: 格式化后的金额字符串
        """
        try:
            # 确保金额是浮点数
            amount = float(amount)
            return f"¥{amount:.2f}"
        except (ValueError, TypeError):
            return "¥0.00"
    
    @staticmethod
    def generate_order_no():
        """
        生成唯一的商户订单号
        
        Returns:
            str: 订单号
        """
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        import random
        random_suffix = random.randint(1000, 9999)
        return f"PAY{timestamp}{random_suffix}"
    
    def sync_payment_status(self, order_no):
        """
        同步支付状态 - 用于解决测试环境回调无法到达的问题
        
        Args:
            order_no: 商户订单号
            
        Returns:
            dict: 同步结果
        """
        try:
            # 查询支付平台订单状态
            result = self.query_order(order_no)
            
            if result.get('code') == '1' and result.get('status') == '1':
                # 支付平台显示已支付，需要更新本地订单状态
                from models.order import Order
                from models import db
                
                order = Order.query.filter_by(order_no=order_no).first()
                if not order:
                    return {
                        'success': False, 
                        'error': f'本地未找到订单: {order_no}'
                    }
                
                # 检查是否需要更新
                if order.payment_status == 'paid':
                    return {
                        'success': True,
                        'message': '订单状态已经是已支付，无需更新',
                        'already_paid': True,
                        'order_status': order.status,
                        'payment_status': order.payment_status
                    }
                
                # 更新订单状态
                old_status = order.status
                old_payment_status = order.payment_status
                
                order.status = 'paid'
                order.payment_status = 'paid' 
                order.payment_method = result.get('type', 'alipay')
                
                db.session.commit()
                
                return {
                    'success': True,
                    'message': '支付状态同步成功',
                    'updated': True,
                    'old_status': old_status,
                    'new_status': order.status,
                    'old_payment_status': old_payment_status,
                    'new_payment_status': order.payment_status,
                    'payment_method': order.payment_method,
                    'platform_data': result
                }
                
            elif result.get('code') == '1' and result.get('status') == '0':
                return {
                    'success': True,
                    'message': '支付平台显示未支付，无需更新',
                    'platform_status': 'unpaid',
                    'platform_data': result
                }
            else:
                return {
                    'success': False,
                    'error': f'查询支付平台失败: {result}',
                    'platform_data': result
                }
                
        except Exception as e:
            current_app.logger.error(f"同步支付状态失败: {str(e)}")
            return {
                'success': False,
                'error': f'同步支付状态失败: {str(e)}'
            }


# 支付服务实例化函数
def get_payment_service():
    """获取支付服务实例"""
    return PaymentService()


# 支付状态映射
PAYMENT_STATUS_MAP = {
    'TRADE_SUCCESS': '支付成功',
    'TRADE_FINISHED': '交易完成',
    'TRADE_CLOSED': '交易关闭',
    'WAIT_BUYER_PAY': '等待付款'
} 