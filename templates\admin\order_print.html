<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单打印 - {{ order.order_no }}</title>
    <style>
        /* 打印专用样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
        }

        .print-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 15mm;
            background: white;
        }

        /* 公司标头 */
        .company-header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .company-info {
            font-size: 11px;
            color: #666;
        }

        /* 订单标题 */
        .order-title {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            margin: 30px 0;
            color: #2c3e50;
        }

        /* 订单信息区域 */
        .order-info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }

        .info-block {
            width: 48%;
        }

        .info-block h3 {
            font-size: 14px;
            font-weight: bold;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .info-row {
            display: flex;
            margin-bottom: 5px;
        }

        .info-label {
            font-weight: bold;
            width: 80px;
            color: #555;
        }

        .info-value {
            flex: 1;
        }

        /* 状态徽章 */
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
            color: white;
        }

        .status-pending { background-color: #f39c12; }
        .status-paid { background-color: #3498db; }
        .status-processing { background-color: #9b59b6; }
        .status-shipped { background-color: #95a5a6; }
        .status-delivered { background-color: #27ae60; }
        .status-cancelled { background-color: #e74c3c; }

        .payment-pending { background-color: #f39c12; }
        .payment-paid { background-color: #27ae60; }
        .payment-failed { background-color: #e74c3c; }
        .payment-refunded { background-color: #95a5a6; }

        /* 商品表格 */
        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            font-size: 11px;
        }

        .products-table th,
        .products-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .products-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }

        .products-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .product-image {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 3px;
        }

        .product-name {
            font-weight: bold;
            margin-bottom: 3px;
        }

        .product-attrs {
            font-size: 10px;
            color: #666;
        }

        /* 金额汇总 */
        .amount-summary {
            margin-top: 30px;
            border: 2px solid #2c3e50;
            padding: 20px;
            background-color: #f8f9fa;
        }

        .amount-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .amount-row.total {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            border-top: 1px solid #ddd;
            padding-top: 8px;
            margin-top: 10px;
        }

        /* 备注区域 */
        .notes-section {
            margin-top: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
        }

        .notes-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        /* 页脚 */
        .print-footer {
            margin-top: 50px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }

        /* 打印样式 */
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .print-container {
                margin: 0;
                padding: 0;
                max-width: none;
            }
            
            .no-print {
                display: none !important;
            }
            
            @page {
                margin: 15mm;
                size: A4;
            }
        }

        /* 屏幕预览样式 */
        @media screen {
            body {
                background-color: #f5f5f5;
                padding: 20px;
            }
            
            .print-container {
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }
            
            .print-actions {
                text-align: center;
                margin-bottom: 20px;
            }
            
            .btn {
                display: inline-block;
                padding: 10px 20px;
                margin: 0 10px;
                background-color: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 5px;
                border: none;
                cursor: pointer;
                font-size: 14px;
            }
            
            .btn:hover {
                background-color: #0056b3;
            }
            
            .btn-secondary {
                background-color: #6c757d;
            }
            
            .btn-secondary:hover {
                background-color: #545b62;
            }
        }
    </style>
</head>
<body>
    <!-- 打印操作按钮（仅屏幕显示） -->
    <div class="print-actions no-print">
        <button class="btn" onclick="window.print()">打印订单</button>
        <a href="{{ url_for('admin.order_detail', id=order.id) }}" class="btn btn-secondary">返回详情</a>
    </div>

    <div class="print-container">
        <!-- 公司标头 -->
        <div class="company-header">
            <div class="company-name">{{ site_config.company_name or '三联纸印刷有限公司' }}</div>
            <div class="company-info">
                电话：{{ site_config.company_phone or '************' }} | 
                地址：{{ site_config.company_address or '中国广东省广州市' }}
            </div>
        </div>

        <!-- 订单标题 -->
        <div class="order-title">订单详情单</div>

        <!-- 订单信息 -->
        <div class="order-info-section">
            <div class="info-block">
                <h3>订单信息</h3>
                <div class="info-row">
                    <span class="info-label">订单号：</span>
                    <span class="info-value">{{ order.order_no }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">下单时间：</span>
                    <span class="info-value">{{ order.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">订单状态：</span>
                    <span class="info-value">
                        <span class="status-badge status-{{ order.status }}">
                            {{ order.get_status_display() }}
                        </span>
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">支付状态：</span>
                    <span class="info-value">
                        <span class="status-badge payment-{{ order.payment_status }}">
                            {{ order.get_payment_status_display() }}
                        </span>
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">支付方式：</span>
                    <span class="info-value">{{ order.payment_method or '未选择' }}</span>
                </div>
            </div>

            <div class="info-block">
                <h3>客户信息</h3>
                <div class="info-row">
                    <span class="info-label">客户姓名：</span>
                    <span class="info-value">{{ order.user.real_name or order.user.username }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">用户名：</span>
                    <span class="info-value">{{ order.user.username }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">联系邮箱：</span>
                    <span class="info-value">{{ order.user.email or '未提供' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">联系电话：</span>
                    <span class="info-value">{{ order.user.phone or '未提供' }}</span>
                </div>
                {% if order.shipping_address %}
                <div class="info-row">
                    <span class="info-label">收货人：</span>
                    <span class="info-value">{{ order.shipping_name or '同客户' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">收货电话：</span>
                    <span class="info-value">{{ order.shipping_phone or '同客户' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">收货地址：</span>
                    <span class="info-value">{{ order.shipping_address }}</span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- 商品明细表格 -->
        <table class="products-table">
            <thead>
                <tr>
                    <th style="width: 50px;">序号</th>
                    <th style="width: 250px;">商品信息</th>
                    <th style="width: 150px;">规格属性</th>
                    <th style="width: 80px;">单价</th>
                    <th style="width: 60px;">数量</th>
                    <th style="width: 80px;">小计</th>
                </tr>
            </thead>
            <tbody>
                {% for item in order.order_items %}
                <tr>
                    <td style="text-align: center;">{{ loop.index }}</td>
                    <td>
                        <div class="product-name">{{ item.product_name }}</div>
                        {% if item.product %}
                        <div style="font-size: 10px; color: #666;">
                            商品编号：{{ item.product.id }}
                        </div>
                        {% else %}
                        <div style="font-size: 10px; color: #e74c3c;">
                            商品已删除
                        </div>
                        {% endif %}
                    </td>
                    <td>
                        <div class="product-attrs">
                            {{ item.get_attributes_display() or '无规格' }}
                        </div>
                    </td>
                    <td style="text-align: right;">¥{{ "%.2f"|format(item.unit_price) }}</td>
                    <td style="text-align: center;">{{ item.quantity }}</td>
                    <td style="text-align: right;">¥{{ "%.2f"|format(item.total_price) }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- 金额汇总 -->
        <div class="amount-summary">
            <div class="amount-row">
                <span>商品总额：</span>
                <span>¥{{ "%.2f"|format(order.total_amount) }}</span>
            </div>
            <div class="amount-row">
                <span>优惠金额：</span>
                <span style="color: #27ae60;">-¥{{ "%.2f"|format(order.discount_amount) }}</span>
            </div>
            <div class="amount-row total">
                <span>实付金额：</span>
                <span>¥{{ "%.2f"|format(order.final_amount) }}</span>
            </div>
        </div>

        <!-- 备注信息 -->
        {% if order.notes %}
        <div class="notes-section">
            <div class="notes-title">订单备注：</div>
            <div>{{ order.notes }}</div>
        </div>
        {% endif %}

        <!-- 页脚 -->
        <div class="print-footer">
            <div>订单打印时间：<span id="print-time"></span></div>
            <div style="margin-top: 10px;">{{ site_config.company_name or '三联纸印刷有限公司' }} - 专业印刷服务</div>
        </div>
    </div>

    <script>
        // 设置打印时间
        document.getElementById('print-time').textContent = new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        
        // 自动打印功能（可选）
        function autoPrint() {
            window.print();
        }
        
        // 如果URL包含auto参数，自动打印
        if (window.location.search.includes('auto=1')) {
            setTimeout(autoPrint, 1000);
        }
    </script>
</body>
</html> 