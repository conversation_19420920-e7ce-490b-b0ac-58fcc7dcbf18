<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="test-csrf-token">
    <title>删除功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>删除功能测试页面</h2>
        <p>这个页面用于测试修复后的删除功能是否正常工作。</p>
        
        <div class="card">
            <div class="card-body">
                <h5>测试项目</h5>
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>名称</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>测试项目1</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-outline-danger delete-btn"
                                        data-url="/admin/test-delete/1"
                                        data-name="测试项目1">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>测试项目2</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-outline-danger delete-btn"
                                        data-url="/admin/test-delete/2"
                                        data-name="测试项目2">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除 "<span id="deleteName"></span>" 吗？</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🔍 初始化删除功能测试');
        
        // 初始化删除功能
        initDeleteFunction();
    });

    function initDeleteFunction() {
        const deleteModal = document.getElementById('deleteModal');
        const deleteNameSpan = document.getElementById('deleteName');
        const confirmDeleteBtn = document.getElementById('confirmDelete');
        let currentDeleteUrl = '';
        let currentDeleteName = '';
        
        if (!deleteModal) {
            console.error('删除模态框未找到');
            return;
        }
        
        const modal = new bootstrap.Modal(deleteModal);
        
        // 移除所有现有的事件监听器
        const deleteButtons = document.querySelectorAll('.delete-btn');
        deleteButtons.forEach(btn => {
            // 克隆节点以移除所有事件监听器
            const newBtn = btn.cloneNode(true);
            btn.parentNode.replaceChild(newBtn, btn);
        });
        
        // 重新绑定删除按钮事件
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                currentDeleteUrl = this.dataset.url;
                currentDeleteName = this.dataset.name;
                
                console.log('点击删除按钮:', currentDeleteName, currentDeleteUrl);
                
                if (deleteNameSpan) {
                    deleteNameSpan.textContent = currentDeleteName;
                }
                
                modal.show();
            });
        });
        
        // 确认删除事件
        if (confirmDeleteBtn) {
            // 移除旧的事件监听器
            const newConfirmBtn = confirmDeleteBtn.cloneNode(true);
            confirmDeleteBtn.parentNode.replaceChild(newConfirmBtn, confirmDeleteBtn);
            
            // 绑定新的事件监听器
            document.getElementById('confirmDelete').addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                if (!currentDeleteUrl) {
                    console.error('删除URL无效');
                    return;
                }
                
                // 禁用按钮，显示加载状态
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>删除中...';
                
                // 获取CSRF token
                const csrfToken = getCSRFToken();
                
                console.log('发送删除请求:', currentDeleteUrl);
                console.log('CSRF Token:', csrfToken);
                
                // 模拟删除请求（实际环境中会发送到服务器）
                setTimeout(() => {
                    console.log('模拟删除成功');
                    this.disabled = false;
                    this.innerHTML = '确认删除';
                    modal.hide();
                    alert('删除成功！（这是模拟结果）');
                }, 2000);
            });
        }
    }

    function getCSRFToken() {
        // 尝试多种方式获取CSRF token
        const metaToken = document.querySelector('meta[name="csrf-token"]');
        if (metaToken) {
            return metaToken.getAttribute('content');
        }
        
        const inputToken = document.querySelector('input[name="csrf_token"]');
        if (inputToken) {
            return inputToken.value;
        }
        
        // 测试环境返回固定值
        return 'test-csrf-token';
    }
    </script>
</body>
</html> 