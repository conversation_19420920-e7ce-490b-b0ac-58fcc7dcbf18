{% extends "base_modern.html" %}

{% block title %}我的订单 - {{ site_config.site_name }}{% endblock %}
{% block description %}查看和管理我的订单{% endblock %}

{% block extra_css %}
<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --glass-bg: rgba(255, 255, 255, 0.1);
        --glass-border: rgba(255, 255, 255, 0.2);
        --shadow-light: rgba(0, 0, 0, 0.1);
    }

    .profile-container {
        background: var(--primary-gradient);
        min-height: 100vh;
        padding: 2rem 0;
        position: relative;
        overflow: hidden;
    }
    
    .profile-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: 
            radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 118, 117, 0.3) 0%, transparent 50%);
        pointer-events: none;
    }
    
    .profile-sidebar {
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        border-radius: 20px;
        padding: 0;
        overflow: hidden;
        box-shadow: 0 8px 32px var(--shadow-light);
    }
    
    .profile-menu-header {
        background: rgba(255, 255, 255, 0.1);
        padding: 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .profile-menu-title {
        color: white;
        font-weight: 600;
        margin: 0;
        font-size: 1.125rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .user-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .profile-menu-item {
        background: transparent;
        border: none;
        color: rgba(255, 255, 255, 0.8);
        padding: 1rem 1.5rem;
        display: flex;
        align-items: center;
        text-decoration: none;
        transition: all 0.3s ease;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }
    
    .profile-menu-item:hover {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        transform: translateX(5px);
    }

    .profile-menu-item.active {
        background: var(--success-gradient);
        color: white;
        box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
    }
    
    .profile-menu-item i {
        margin-right: 0.75rem;
        font-size: 1rem;
    }
    
    .profile-content {
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        border-radius: 20px;
        box-shadow: 0 8px 32px var(--shadow-light);
        overflow: hidden;
    }
    
    .profile-content-header {
        background: rgba(255, 255, 255, 0.1);
        padding: 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .profile-content-title {
        color: white;
        font-weight: 600;
        margin: 0;
        font-size: 1.25rem;
        display: flex;
        align-items: center;
    }

    .profile-content-title i {
        margin-right: 0.75rem;
        color: #4facfe;
    }

    .profile-content-body {
        padding: 2rem;
    }
    
    .order-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        padding: 1rem 1.5rem;
        margin-bottom: 0.75rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .order-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        background: rgba(255, 255, 255, 0.15);
    }
    
    .order-left {
        display: flex;
        align-items: center;
        gap: 2rem;
        flex: 1;
        min-width: 0;
    }
    
    .order-info {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .order-number {
        font-family: 'Courier New', monospace;
        font-weight: 600;
        color: white;
        font-size: 1rem;
    }
    
    .order-date {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.85rem;
    }
    
    .order-amount-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.25rem;
    }
    
    .order-amount {
        font-size: 1.1rem;
        font-weight: 700;
        color: #FFD700;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .order-item-count {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.8rem;
    }
    
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.4rem 0.8rem;
        border-radius: 10px;
        font-weight: 600;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        backdrop-filter: blur(10px);
        white-space: nowrap;
    }
    
    .status-pending { 
        background: rgba(255, 193, 7, 0.3); 
        color: #fff3cd; 
        border: 1px solid rgba(255, 193, 7, 0.5); 
    }
    .status-paid { 
        background: rgba(13, 202, 240, 0.3); 
        color: #b6effb; 
        border: 1px solid rgba(13, 202, 240, 0.5); 
    }
    .status-processing { 
        background: rgba(102, 126, 234, 0.3); 
        color: #c7d2fe; 
        border: 1px solid rgba(102, 126, 234, 0.5); 
    }
    .status-shipped { 
        background: rgba(108, 117, 125, 0.3); 
        color: #d1ecf1; 
        border: 1px solid rgba(108, 117, 125, 0.5); 
    }
    .status-delivered { 
        background: rgba(25, 135, 84, 0.3); 
        color: #d1e7dd; 
        border: 1px solid rgba(25, 135, 84, 0.5); 
    }
    .status-cancelled { 
        background: rgba(220, 53, 69, 0.3); 
        color: #f8d7da; 
        border: 1px solid rgba(220, 53, 69, 0.5); 
    }
    
    .order-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
        flex-shrink: 0;
    }
    
    .btn-action {
        background: var(--success-gradient);
        color: white;
        border: none;
        padding: 0.4rem 0.8rem;
        border-radius: 8px;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.4rem;
        font-size: 0.8rem;
        box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
        white-space: nowrap;
    }
    
    .btn-action:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(79, 172, 254, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .btn-secondary {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .btn-secondary:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;
    }
    
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: rgba(255, 255, 255, 0.7);
    }
    
    .empty-state i {
        font-size: 4rem;
        color: rgba(255, 255, 255, 0.3);
        margin-bottom: 1rem;
    }
    
    .empty-state h3 {
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 0.5rem;
    }
    
    .pagination-modern {
        display: flex;
        justify-content: center;
        margin-top: 2rem;
        padding: 1rem;
    }
    
    .pagination-modern .pagination {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 0.5rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .pagination-modern .page-link {
        border: none;
        border-radius: 10px;
        margin: 0 0.25rem;
        color: white;
        background: transparent;
        transition: all 0.3s ease;
    }
    
    .pagination-modern .page-link:hover {
        background: var(--success-gradient);
        color: white;
        transform: translateY(-1px);
    }
    
    .pagination-modern .page-item.active .page-link {
        background: var(--success-gradient);
        color: white;
        box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
    }
    
    @media (max-width: 768px) {
        .profile-container {
            padding: 1rem 0;
        }
        
        .profile-content-body {
            padding: 1rem;
        }
        
        .order-card {
            flex-direction: column;
            align-items: stretch;
            gap: 0.75rem;
            padding: 1rem;
        }
        
        .order-left {
            flex-direction: column;
            gap: 0.75rem;
        }
        
        .order-amount-info {
            flex-direction: row;
            justify-content: space-between;
        }
        
        .order-actions {
            justify-content: center;
            flex-wrap: wrap;
        }
    }
    
    @media (max-width: 576px) {
        .order-card {
            padding: 0.75rem;
        }
        
        .order-left {
            gap: 0.5rem;
        }
        
        .order-info {
            gap: 0.2rem;
        }
        
        .order-number {
            font-size: 0.9rem;
        }
        
        .order-date {
            font-size: 0.8rem;
        }
        
        .order-amount {
            font-size: 1rem;
        }
        
        .btn-action {
            font-size: 0.75rem;
            padding: 0.3rem 0.6rem;
        }
        
        .status-badge {
            font-size: 0.7rem;
            padding: 0.3rem 0.6rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="profile-container">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-4 mb-4">
            <!-- 用户菜单 -->
                <div class="profile-sidebar">
                    <div class="profile-menu-header">
                        <div class="user-avatar">
                                {{ (current_user.real_name or current_user.username)[0].upper() }}
                            </div>
                        <h6 class="profile-menu-title">
                            <div>
                                <div>{{ current_user.real_name or current_user.username }}</div>
                                <small style="opacity: 0.8; font-size: 0.8rem;">用户中心</small>
                            </div>
                        </h6>
                    </div>
                    <div class="profile-menu">
                        <a href="{{ url_for('auth.profile') }}" class="profile-menu-item">
                                <i class="fas fa-user"></i>个人资料
                            </a>
                        <a href="{{ url_for('auth.change_password') }}" class="profile-menu-item">
                                <i class="fas fa-lock"></i>修改密码
                            </a>
                        <a href="{{ url_for('main.orders') }}" class="profile-menu-item active">
                                <i class="fas fa-shopping-bag"></i>我的订单
                            </a>
                        <a href="{{ url_for('main.products') }}" class="profile-menu-item">
                            <i class="fas fa-box"></i>商品
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-9 col-md-8">
                <div class="profile-content">
                    <div class="profile-content-header">
                        <h5 class="profile-content-title">
                            <i class="fas fa-shopping-bag"></i>我的订单
                        </h5>
                    </div>
                    <div class="profile-content-body">
                        {% if orders.items %}
                        <!-- 订单卡片列表 -->
                        {% for order in orders.items %}
                        <div class="order-card">
                            <div class="order-left">
                                <div class="order-info">
                                    <div class="order-number">订单号：{{ order.order_no }}</div>
                                    <div class="order-date">{{ format_china_date(order.created_at) }}</div>
                            </div>
                            
                                <div class="order-amount-info">
                                    <div class="order-amount">¥{{ "%.2f"|format(order.final_amount) }}</div>
                                    <div class="order-item-count">
                                        {% set item_count = order.order_items.count() %}
                                        共{{ item_count }}件商品
                                    </div>
                                </div>
                            </div>
                            
                            <div class="status-badge status-{{ order.status }}">
                                <i class="fas fa-circle me-2" style="font-size: 0.4rem;"></i>
                                {{ order.get_status_display() }}
                                </div>
                                
                                <div class="order-actions">
                                    <a href="{{ url_for('main.order_detail', id=order.id) }}" class="btn-action">
                                        <i class="fas fa-eye"></i>
                                        查看详情
                                    </a>
                                    
                                    {% if order.can_pay() %}
                                    <a href="{{ url_for('main.payment_page', order_id=order.id) }}" class="btn-action">
                                        <i class="fas fa-credit-card"></i>
                                        立即支付
                                    </a>
                                    {% endif %}
                                    
                                    {% if order.can_cancel() %}
                                    <a href="#" class="btn-action btn-secondary" onclick="cancelOrder('{{ order.id }}')">
                                        <i class="fas fa-times"></i>
                                        取消订单
                                    </a>
                                    {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                        
                        <!-- 分页 -->
                        {% if orders.pages > 1 %}
                        <div class="pagination-modern">
                            <nav aria-label="订单分页">
                                <ul class="pagination">
                                    {% if orders.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main.orders', page=orders.prev_num) }}">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                    {% endif %}
                                    
                                    {% for page_num in orders.iter_pages() %}
                                        {% if page_num %}
                                            {% if page_num != orders.page %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('main.orders', page=page_num) }}">{{ page_num }}</a>
                                            </li>
                                            {% else %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ page_num }}</span>
                                            </li>
                                            {% endif %}
                                        {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if orders.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main.orders', page=orders.next_num) }}">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                        {% endif %}
                        
                        {% else %}
                        <!-- 空状态 -->
                        <div class="empty-state">
                            <i class="fas fa-shopping-bag"></i>
                            <h3>暂无订单</h3>
                            <p>您还没有任何订单，去看看有什么好商品吧！</p>
                            <a href="{{ url_for('main.products') }}" class="btn-action mt-3">
                                <i class="fas fa-shopping-cart"></i>
                                去购物
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function cancelOrder(orderId) {
    if (confirm('确认要取消此订单吗？取消后不可恢复！')) {
        fetch(`/api/orders/${orderId}/cancel`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
                              '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('success', '订单已取消');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showNotification('error', data.message || '取消失败');
            }
        })
        .catch(error => {
            console.error('取消订单失败:', error);
            showNotification('error', '取消失败，请重试');
        });
    }
}

function showNotification(type, message) {
    if (window.ModernUI && window.ModernUI.notification) {
        window.ModernUI.notification[type](message);
    } else if (window.ModernNotification) {
        window.ModernNotification[type](message);
    } else {
        // 简化版通知，使用浮动提示
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 9999;
            min-width: 250px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            transform: translateX(100%);
        `;
        
        if (type === 'success') {
            notification.style.background = 'linear-gradient(135deg, #4ade80, #22c55e)';
            notification.innerHTML = `<i class="fas fa-check-circle" style="margin-right: 8px;"></i>${message}`;
        } else if (type === 'error') {
            notification.style.background = 'linear-gradient(135deg, #ef4444, #dc2626)';
            notification.innerHTML = `<i class="fas fa-exclamation-circle" style="margin-right: 8px;"></i>${message}`;
        } else {
            notification.style.background = 'linear-gradient(135deg, #3b82f6, #2563eb)';
            notification.innerHTML = `<i class="fas fa-info-circle" style="margin-right: 8px;"></i>${message}`;
            }
        
        document.body.appendChild(notification);
        
        // 动画显示
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // 自动消失
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}
</script>
{% endblock %}
