#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from models.order import Order
from models import db
from app import create_app

def update_order_status(order_no, new_status='paid', payment_status='paid', payment_method='alipay'):
    app = create_app()
    with app.app_context():
        order = Order.query.filter_by(order_no=order_no).first()
        if order:
            print(f"找到订单: {order.order_no}")
            print(f"当前状态: {order.status} -> {order.get_status_display()}")
            print(f"当前支付状态: {order.payment_status} -> {order.get_payment_status_display()}")
            
            # 更新状态
            old_status = order.status
            old_payment_status = order.payment_status
            
            order.status = new_status
            order.payment_status = payment_status
            order.payment_method = payment_method
            
            try:
                db.session.commit()
                print(f"状态更新成功!")
                print(f"订单状态: {old_status} -> {order.status} ({order.get_status_display()})")
                print(f"支付状态: {old_payment_status} -> {order.payment_status} ({order.get_payment_status_display()})")
                print(f"支付方式: {order.payment_method}")
                print(f"是否可以支付: {order.can_pay()}")
                return True
            except Exception as e:
                db.session.rollback()
                print(f"更新失败: {e}")
                return False
        else:
            print(f"未找到订单: {order_no}")
            return False

if __name__ == "__main__":
    order_no = "ORDER08FD007C0003"
    
    # 手动更新订单状态为已支付
    result = update_order_status(order_no, 'paid', 'paid', 'alipay')
    
    if result:
        print("\n" + "="*50)
        print("订单状态更新完成! 现在用户应该可以看到订单已支付。")
    else:
        print("订单状态更新失败!") 