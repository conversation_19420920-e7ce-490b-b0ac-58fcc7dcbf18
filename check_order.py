#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from models.order import Order
from models import db
from app import create_app

def check_order(order_no):
    app = create_app()
    with app.app_context():
        order = Order.query.filter_by(order_no=order_no).first()
        if order:
            print(f"订单号: {order.order_no}")
            print(f"状态 (status): {order.status}")
            print(f"支付状态 (payment_status): {order.payment_status}")
            print(f"支付方式 (payment_method): {order.payment_method}")
            print(f"状态显示: {order.get_status_display()}")
            print(f"支付状态显示: {order.get_payment_status_display()}")
            print(f"可以支付: {order.can_pay()}")
            print(f"金额: {order.final_amount}")
            print(f"创建时间: {order.created_at}")
            print(f"更新时间: {order.updated_at}")
        else:
            print(f"未找到订单: {order_no}")

if __name__ == "__main__":
    order_no = "ORDER08FD007C0003"
    check_order(order_no) 