#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from services.payment import get_payment_service
from app import create_app

def query_payment_status(order_no):
    app = create_app()
    with app.app_context():
        payment_service = get_payment_service()
        
        print(f"查询订单: {order_no}")
        print(f"支付网关: {payment_service.gateway}")
        print(f"商户ID: {payment_service.pid}")
        print("=" * 50)
        
        # 查询订单状态
        result = payment_service.query_order(order_no)
        print("支付平台查询结果:")
        print(result)
        
        print("=" * 50)
        
        # 查询商户信息
        merchant_info = payment_service.query_merchant_info()
        print("商户信息查询结果:")
        print(merchant_info)

if __name__ == "__main__":
    order_no = "ORDER08FD007C0003"
    query_payment_status(order_no) 