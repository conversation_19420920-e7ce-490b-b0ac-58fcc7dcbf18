{% extends "admin/base.html" %}

{% block title %}属性管理 - 管理后台{% endblock %}

{% block content %}

<!-- 简洁的上下文信息 -->
{% if current_group %}
<div class="alert alert-info mb-3">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h5 class="mb-1">
                <i class="fas fa-tags me-2"></i>小属性管理
            </h5>
            <p class="mb-0">
                <strong>商品分类：</strong>{{ current_group.category.name if current_group.category else '未分类' }}
                →
                <strong>属性组：</strong>{{ current_group.name }}
            </p>
        </div>
        <div class="col-md-4 text-end">
            <span class="badge bg-primary fs-6">{{ attributes.total }} 个属性</span>
        </div>
    </div>
</div>
{% endif %}

<!-- 操作按钮 -->
<div class="row mb-3">
    <div class="col-md-6">
        {% if current_group %}
        <a href="{{ url_for('admin.attribute_groups', category_id=current_group.category_id if current_group.category_id else '') }}" 
           class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回属性组管理
        </a>
        {% else %}
        <a href="{{ url_for('admin.attribute_groups') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回属性组管理
        </a>
        {% endif %}
        </div>
    <div class="col-md-6 text-end">
        {% if current_group %}
        <a href="{{ url_for('admin.add_attribute', group_id=current_group.id) }}" 
           class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>添加属性
        </a>
        {% else %}
        <a href="{{ url_for('admin.add_attribute') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>添加属性
        </a>
        {% endif %}
    </div>
</div>

<!-- 属性筛选 -->
{% if not current_group %}
<div class="card mb-3">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-6">
                <label class="form-label">按属性组筛选</label>
                <select name="group_id" class="form-select" onchange="this.form.submit()">
                    <option value="">-- 所有属性组 --</option>
                    {% for group in groups %}
                    <option value="{{ group.id }}" {{ 'selected' if group.id == current_group_id }}>
                        {{ group.category.name if group.category else '未分类' }} - {{ group.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-6 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">筛选</button>
                <a href="{{ url_for('admin.attributes') }}" class="btn btn-outline-secondary">清除</a>
            </div>
        </form>
    </div>
</div>
{% endif %}

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            {% if current_group %}
                "{{ current_group.name }}" 的属性列表
            {% else %}
                属性列表
            {% endif %}
        </h5>
    </div>

    <div class="card-body">
        {% if attributes.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>所属分类</th>
                        <th>属性组</th>
                        <th>属性名称</th>
                        <th>属性值</th>
                        <th>价格调整</th>
                        <th>排序</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for attribute in attributes.items %}
                    <tr>
                        <td>{{ attribute.id }}</td>
                        <td>
                            {% if current_group %}
                            <span class="badge bg-primary">{{ current_group.category.name if current_group.category else '未分类' }}</span>
                            {% else %}
                            <span class="badge bg-secondary">{{ attribute.group.category.name if attribute.group.category else '未分类' }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if current_group %}
                            <span class="badge bg-warning text-dark">{{ current_group.name }}</span>
                            {% else %}
                            <span class="badge bg-outline-secondary">{{ attribute.group.name }}</span>
                            {% endif %}
                        </td>
                        <td><strong>{{ attribute.name }}</strong></td>
                        <td>{{ attribute.value or '-' }}</td>
                        <td>
                            {% if attribute.price_modifier != 0 %}
                                {% if attribute.price_modifier_type == 'percentage' %}
                                    <span class="badge bg-info">+{{ attribute.price_modifier }}%</span>
                                {% else %}
                                    <span class="badge bg-success">+¥{{ attribute.price_modifier }}</span>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">无调整</span>
                            {% endif %}
                        </td>
                        <td>{{ attribute.sort_order }}</td>
                        <td>
                            {% if attribute.is_active %}
                                <span class="badge bg-success">启用</span>
                            {% else %}
                                <span class="badge bg-danger">禁用</span>
                            {% endif %}
                        </td>
                        <td>{{ attribute.created_at.strftime('%Y-%m-%d') if attribute.created_at else '-' }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('admin.edit_attribute', id=attribute.id) }}" 
                                   class="btn btn-sm btn-outline-primary">编辑</a>
                            <button type="button" class="btn btn-sm btn-outline-danger delete-btn"
                                    data-url="{{ url_for('admin.delete_attribute', id=attribute.id) }}"
                                        data-name="{{ attribute.name }}">删除</button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {% if attributes.pages > 1 %}
        <nav aria-label="属性分页" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if attributes.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.attributes', page=attributes.prev_num, group_id=current_group_id if current_group_id else '') }}">上一页</a>
                </li>
                {% endif %}
                
                {% for page_num in attributes.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != attributes.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.attributes', page=page_num, group_id=current_group_id if current_group_id else '') }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if attributes.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.attributes', page=attributes.next_num, group_id=current_group_id if current_group_id else '') }}">下一页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">
                {% if current_group %}
                    "{{ current_group.name }}" 属性组暂无属性
                {% else %}
                    暂无属性数据
                {% endif %}
            </h5>
            <p class="text-muted mb-4">
                {% if current_group %}
                    点击下方按钮为此属性组添加第一个属性
                {% else %}
                    请先创建属性组，然后添加相应的属性
                {% endif %}
            </p>
            {% if current_group %}
            <a href="{{ url_for('admin.add_attribute', group_id=current_group.id) }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>添加第一个属性
            </a>
            {% else %}
            <a href="{{ url_for('admin.attribute_groups') }}" class="btn btn-primary">
                <i class="fas fa-layer-group me-2"></i>管理属性组
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除属性 "<span id="deleteAttributeName"></span>" 吗？</p>
                <p class="text-muted small">此操作不可恢复，请谨慎操作。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>

<script>
// 删除功能
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 初始化属性页面删除功能');
    
    // 初始化删除功能
    initDeleteFunction();
});

function initDeleteFunction() {
    const deleteModal = document.getElementById('deleteModal');
    const deleteNameSpan = document.getElementById('deleteAttributeName');
    const confirmDeleteBtn = document.getElementById('confirmDelete');
    let currentDeleteUrl = '';
    let currentDeleteName = '';
    
    if (!deleteModal) {
        console.error('删除模态框未找到');
        return;
    }
    
    const modal = new bootstrap.Modal(deleteModal);
    
    // 移除所有现有的事件监听器
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(btn => {
        // 克隆节点以移除所有事件监听器
        const newBtn = btn.cloneNode(true);
        btn.parentNode.replaceChild(newBtn, btn);
    });
    
    // 重新绑定删除按钮事件
    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            currentDeleteUrl = this.dataset.url;
            currentDeleteName = this.dataset.name;
            
            console.log('点击删除按钮:', currentDeleteName, currentDeleteUrl);
            
            if (deleteNameSpan) {
                deleteNameSpan.textContent = currentDeleteName;
            }
            
            modal.show();
        });
    });
    
    // 确认删除事件
    if (confirmDeleteBtn) {
        // 移除旧的事件监听器
        const newConfirmBtn = confirmDeleteBtn.cloneNode(true);
        confirmDeleteBtn.parentNode.replaceChild(newConfirmBtn, confirmDeleteBtn);
        
        // 绑定新的事件监听器
        document.getElementById('confirmDelete').addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            if (!currentDeleteUrl) {
                showAlert('danger', '删除URL无效');
                return;
            }
            
            // 禁用按钮，显示加载状态
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>删除中...';
            
            // 获取CSRF token
            const csrfToken = getCSRFToken();
            
            console.log('发送删除请求:', currentDeleteUrl);
            console.log('CSRF Token:', csrfToken);
            
            // 发送删除请求
            fetch(currentDeleteUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken,
                    'Accept': 'application/json'
                },
                credentials: 'same-origin'
            })
            .then(response => {
                console.log('响应状态:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return response.json();
            })
            .then(data => {
                console.log('删除响应:', data);
                
                if (data.success) {
                    showAlert('success', data.message || '删除成功');
                    // 延迟刷新页面
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showAlert('danger', data.message || '删除失败');
                    this.disabled = false;
                    this.innerHTML = '确认删除';
                }
            })
            .catch(error => {
                console.error('删除请求错误:', error);
                showAlert('danger', `删除失败: ${error.message}`);
                this.disabled = false;
                this.innerHTML = '确认删除';
            });
            
            // 隐藏模态框
            modal.hide();
        });
    }
}

function getCSRFToken() {
    // 尝试多种方式获取CSRF token
    const metaToken = document.querySelector('meta[name="csrf-token"]');
    if (metaToken) {
        return metaToken.getAttribute('content');
    }
    
    const inputToken = document.querySelector('input[name="csrf_token"]');
    if (inputToken) {
        return inputToken.value;
    }
    
    // 最后尝试模板变量
    return '{{ csrf_token() }}';
}

function showAlert(type, message) {
    // 移除现有的alert
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());
    
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert" style="margin-bottom: 1rem;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertAdjacentHTML('afterbegin', alertHtml);
        
        // 滚动到顶部显示消息
        window.scrollTo({ top: 0, behavior: 'smooth' });
        
        // 自动关闭
        setTimeout(() => {
            const alert = document.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}
</script>

{% endblock %}
