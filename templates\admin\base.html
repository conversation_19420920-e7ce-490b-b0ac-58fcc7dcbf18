<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}管理后台 - {{ site_config.site_name }}{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="{{ url_for("static", filename="vendor/bootstrap/bootstrap.min.css") }}" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="{{ url_for("static", filename="vendor/fontawesome/css/all.min.css") }}" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='css/admin.css') }}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('admin.index') }}">
                <i class="fas fa-cogs me-2"></i>管理后台
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>{{ current_user.real_name or current_user.username }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('main.index') }}">
                            <i class="fas fa-home me-2"></i>前台首页
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                            <i class="fas fa-user me-2"></i>个人资料
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                            <i class="fas fa-sign-out-alt me-2"></i>退出登录
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.endpoint == 'admin.index' else '' }}" 
                               href="{{ url_for('admin.index') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>仪表盘
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                                <span>商品管理</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'categories' in request.endpoint else '' }}" 
                               href="{{ url_for('admin.categories') }}">
                                <i class="fas fa-tags me-2"></i>分类管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'attribute' in request.endpoint else '' }}" 
                               href="{{ url_for('admin.attribute_groups') }}">
                                <i class="fas fa-list me-2"></i>属性管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'products' in request.endpoint else '' }}"
                               href="{{ url_for('admin.products') }}">
                                <i class="fas fa-box me-2"></i>商品管理
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                                <span>订单管理</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'orders' in request.endpoint else '' }}" 
                               href="{{ url_for('admin.orders') }}">
                                <i class="fas fa-shopping-cart me-2"></i>订单列表
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                                <span>系统管理</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'users' in request.endpoint else '' }}" 
                               href="{{ url_for('admin.users') }}">
                                <i class="fas fa-users me-2"></i>用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'settings' in request.endpoint else '' }}" 
                               href="{{ url_for('admin.settings') }}">
                                <i class="fas fa-cog me-2"></i>系统设置
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- 主要内容 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- 消息提示 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                    <div class="mt-3">
                        {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                {% endwith %}
                
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="{{ url_for("static", filename="vendor/bootstrap/bootstrap.bundle.min.js") }}"></script>
    <!-- jQuery -->
    <script src="{{ url_for("static", filename="vendor/jquery/jquery.min.js") }}"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/admin.js') }}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
