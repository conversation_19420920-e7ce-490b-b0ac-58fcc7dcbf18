#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
"""

import pymysql
import os

def init_database():
    """初始化数据库"""
    try:
        # 读取SQL文件
        sql_file = os.path.join('sql', 'init_database.sql')
        if not os.path.exists(sql_file):
            print(f"SQL文件不存在: {sql_file}")
            return False
            
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 连接MySQL数据库
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            charset='utf8mb4'
        )
        
        cursor = conn.cursor()
        
        # 分割SQL语句并执行
        sql_statements = sql_content.split(';')
        
        for statement in sql_statements:
            statement = statement.strip()
            if statement and not statement.startswith('--'):
                try:
                    cursor.execute(statement)
                    print(f"执行成功: {statement[:50]}...")
                except Exception as e:
                    print(f"执行失败: {statement[:50]}... -> {e}")
        
        # 提交事务
        conn.commit()
        print("数据库初始化完成！")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        return False

if __name__ == '__main__':
    if init_database():
        print("✅ 数据库初始化成功，现在可以启动应用了！")
    else:
        print("❌ 数据库初始化失败，请检查错误信息！") 