<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="#667eea">
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='images/favicon.svg') }}">
    
    <title>{% block title %}{{ site_config.site_name }}{% endblock %}</title>
    <meta name="description" content="{% block description %}{{ site_config.site_description }}{% endblock %}">
    <meta name="keywords" content="{% block keywords %}印刷,定制,打印服务,企业印刷{% endblock %}">
    
    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    
    <!-- Modern CSS Framework -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Modern Design System -->
    <link href="{{ url_for('static', filename='css/modern-design-system.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/modern-components.css') }}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --hero-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            
            --glass-white: rgba(255, 255, 255, 0.25);
            --glass-dark: rgba(0, 0, 0, 0.1);
            
            --shadow-sm: 0 2px 8px rgba(0,0,0,0.06);
            --shadow-md: 0 8px 25px rgba(0,0,0,0.1);
            --shadow-lg: 0 15px 40px rgba(0,0,0,0.12);
            --shadow-xl: 0 25px 60px rgba(0,0,0,0.15);
            --shadow-2xl: 0 40px 80px rgba(0,0,0,0.18);
            
            --border-radius-sm: 8px;
            --border-radius-md: 12px;
            --border-radius-lg: 16px;
            --border-radius-xl: 24px;
            --border-radius-2xl: 32px;
            
            --transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.6;
            color: #1a202c;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }

        /* Glass Morphism Navigation */
        .navbar-glass {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-lg);
        }

        .navbar-brand {
            font-family: 'Poppins', sans-serif;
            font-weight: 700;
            font-size: 1.5rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-link {
            font-weight: 500;
            transition: var(--transition-base);
            position: relative;
            color: #4a5568 !important;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 50%;
            background: var(--primary-gradient);
            transition: var(--transition-base);
            transform: translateX(-50%);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }

        .nav-link:hover {
            color: #667eea !important;
            transform: translateY(-2px);
        }

        /* Modern Search Bar */
        .search-container {
            position: relative;
            max-width: 400px;
        }

        .search-input {
            border: none;
            border-radius: var(--border-radius-xl);
            padding: 0.75rem 1rem 0.75rem 3rem;
            background: rgba(255, 255, 255, 0.9);
            box-shadow: var(--shadow-sm);
            transition: var(--transition-base);
            width: 100%;
        }

        .search-input:focus {
            outline: none;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            background: white;
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
        }

        /* Products Badge */
        .products-badge {
            background: var(--danger-gradient) !important;
            border: 2px solid white;
            box-shadow: var(--shadow-md);
        }

        /* Modern Alerts */
        .alert-modern {
            border: none;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-left: 4px solid;
        }

        .alert-success.alert-modern {
            background: rgba(16, 185, 129, 0.1);
            border-left-color: #10b981;
            color: #065f46;
        }

        .alert-danger.alert-modern {
            background: rgba(239, 68, 68, 0.1);
            border-left-color: #ef4444;
            color: #991b1b;
        }

        .alert-info.alert-modern {
            background: rgba(59, 130, 246, 0.1);
            border-left-color: #3b82f6;
            color: #1e40af;
        }

        .alert-warning.alert-modern {
            background: rgba(245, 158, 11, 0.1);
            border-left-color: #f59e0b;
            color: #92400e;
        }

        /* Floating Action Button */
        .fab-container {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 1000;
        }

        .fab {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary-gradient);
            color: white;
            border: none;
            box-shadow: var(--shadow-xl);
            cursor: pointer;
            transition: var(--transition-base);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .fab:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-2xl);
        }

        /* Page Transitions */
        .page-enter {
            animation: pageEnter 0.6s ease-out;
        }

        @keyframes pageEnter {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Modern Dropdown */
        .dropdown-menu {
            border: none;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.95);
            padding: 0.5rem;
        }

        .dropdown-item {
            border-radius: var(--border-radius-md);
            margin-bottom: 0.25rem;
            transition: var(--transition-base);
        }

        .dropdown-item:hover {
            background: var(--primary-gradient);
            color: white;
            transform: translateX(2px);
        }

        /* Loading Animation */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(102, 126, 234, 0.3);
            border-radius: 50%;
            border-top-color: #667eea;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .navbar-brand {
                font-size: 1.2rem;
            }
            
            .search-container {
                max-width: 100%;
                margin: 1rem 0;
            }
            
            .fab-container {
                bottom: 1rem;
                right: 1rem;
            }
            
            .fab {
                width: 50px;
                height: 50px;
                font-size: 1rem;
            }
        }

        /* Dark Mode Support */
        [data-bs-theme="dark"] {
            --glass-white: rgba(0, 0, 0, 0.25);
            --glass-dark: rgba(255, 255, 255, 0.1);
        }

        [data-bs-theme="dark"] .navbar-glass {
            background: rgba(26, 32, 44, 0.95) !important;
            border-bottom-color: rgba(255, 255, 255, 0.1);
        }

        [data-bs-theme="dark"] .nav-link {
            color: #e2e8f0 !important;
        }

        [data-bs-theme="dark"] .search-input {
            background: rgba(45, 55, 72, 0.9);
            color: #e2e8f0;
        }

        /* Smooth Scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Selection */
        ::selection {
            background: rgba(102, 126, 234, 0.3);
            color: #1a202c;
        }

        ::-moz-selection {
            background: rgba(102, 126, 234, 0.3);
            color: #1a202c;
        }
    </style>
</head>
<body>
    <!-- Modern Navigation -->
    <nav class="navbar navbar-expand-lg navbar-glass sticky-top">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="fas fa-print me-2"></i>{{ site_config.site_name }}
            </a>

            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'main.index' }}" href="{{ url_for('main.index') }}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-th-large me-1"></i>商品分类
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('main.products') }}">
                                <i class="fas fa-list me-2"></i>全部商品
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            {% for category in main_categories %}
                            <li><a class="dropdown-item" href="{{ url_for('main.products', category=category.id) }}">
                                <i class="fas fa-tag me-2"></i>{{ category.name }}
                            </a></li>
                            {% endfor %}
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'main.products' }}" href="{{ url_for('main.products') }}">
                            <i class="fas fa-shopping-bag me-1"></i>商品中心
                        </a>
                    </li>
                </ul>

                <!-- Modern Search -->
                <div class="search-container me-3">
                    <form method="GET" action="{{ url_for('main.products') }}">
                        <div class="position-relative">
                            <i class="fas fa-search search-icon"></i>
                            <input class="search-input" type="search" name="search"
                                   placeholder="搜索商品..." value="{{ request.args.get('search', '') }}"
                                   autocomplete="off">
                        </div>
                    </form>
                </div>

                <ul class="navbar-nav align-items-center">
                    <!-- Theme Toggle -->
                    <li class="nav-item me-3">
                        <button class="btn btn-link nav-link border-0" onclick="toggleTheme()" title="切换主题">
                            <i class="fas fa-moon" id="theme-icon"></i>
                        </button>
                    </li>

                    <!-- Quick Contact -->
                    <li class="nav-item me-3">
                        <div class="dropdown">
                            <button class="btn btn-link nav-link border-0 position-relative"
                                    data-bs-toggle="dropdown"
                                    aria-expanded="false"
                                    title="快速联系">
                                <i class="fas fa-phone"></i>
                            </button>
                            <div class="dropdown-menu quick-contact-menu">
                                <h6 class="dropdown-header">
                                    <i class="fas fa-headset me-2"></i>联系我们
                                </h6>
                                <div class="px-3 py-2">
                                    <div class="contact-row">
                                        <i class="fas fa-phone me-2 text-primary"></i>
                                        <a href="tel:{{ site_config.company_phone }}" class="text-decoration-none">
                                            {{ site_config.company_phone }}
                                        </a>
                                    </div>
                                    <div class="contact-row">
                                        <i class="fas fa-envelope me-2 text-primary"></i>
                                        <a href="mailto:{{ site_config.contact_email }}" class="text-decoration-none">
                                            {{ site_config.contact_email }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>

                    <!-- Cart -->
                    {% if current_user.is_authenticated %}
                    <li class="nav-item me-3">
                        <a class="nav-link position-relative" href="{{ url_for('main.cart') }}" title="购物车">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill products-badge">
                                {{ current_user.get_cart_count() }}
                            </span>
                        </a>
                    </li>
                    {% endif %}

                    <!-- User Menu -->
                    {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle user-menu" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>{{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu user-dropdown">
                            <li><h6 class="dropdown-header">
                                <i class="fas fa-user-circle me-2"></i>{{ current_user.username }}
                            </h6></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('main.orders') }}">
                                <i class="fas fa-list-alt me-2"></i>我的订单
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('main.cart') }}">
                                <i class="fas fa-shopping-cart me-2"></i>购物车
                            </a></li>
                            {% if current_user.is_admin %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('admin.dashboard') }}">
                                <i class="fas fa-cog me-2"></i>管理后台
                            </a></li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>退出登录
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">
                            <i class="fas fa-sign-in-alt me-1"></i>登录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.register') }}">
                            <i class="fas fa-user-plus me-1"></i>注册
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        <div class="container mt-3">
            {% for category, message in messages %}
            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-modern alert-dismissible fade show" role="alert">
                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'info-circle' if category == 'info' else 'check-circle' if category == 'success' else 'exclamation-triangle' }} me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main class="page-enter">
        {% block content %}{% endblock %}
    </main>

    <!-- Modern Footer -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="mb-3">
                        <i class="fas fa-print me-2"></i>{{ site_config.site_name }}
                    </h5>
                    <p class="text-muted">{{ site_config.site_description }}</p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-light" title="微信">
                            <i class="fab fa-weixin fa-lg"></i>
                        </a>
                        <a href="#" class="text-light" title="QQ">
                            <i class="fab fa-qq fa-lg"></i>
                        </a>
                        <a href="tel:{{ site_config.company_phone }}" class="text-light" title="电话">
                            <i class="fas fa-phone fa-lg"></i>
                        </a>
                        <a href="mailto:{{ site_config.contact_email }}" class="text-light" title="邮箱">
                            <i class="fas fa-envelope fa-lg"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">商品分类</h6>
                    <ul class="list-unstyled">
                        {% for category in main_categories[:5] %}
                        <li class="mb-2">
                            <a href="{{ url_for('main.products', category=category.id) }}" class="text-muted text-decoration-none">
                                {{ category.name }}
                            </a>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">快速链接</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="{{ url_for('main.index') }}" class="text-muted text-decoration-none">首页</a></li>
                        <li class="mb-2"><a href="{{ url_for('main.products') }}" class="text-muted text-decoration-none">商品中心</a></li>
                        {% if current_user.is_authenticated %}
                        <li class="mb-2"><a href="{{ url_for('main.orders') }}" class="text-muted text-decoration-none">我的订单</a></li>
                        <li class="mb-2"><a href="{{ url_for('main.cart') }}" class="text-muted text-decoration-none">购物车</a></li>
                        {% else %}
                        <li class="mb-2"><a href="{{ url_for('auth.login') }}" class="text-muted text-decoration-none">登录</a></li>
                        <li class="mb-2"><a href="{{ url_for('auth.register') }}" class="text-muted text-decoration-none">注册</a></li>
                        {% endif %}
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h6 class="mb-3">联系信息</h6>
                    <div class="contact-info">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-map-marker-alt me-3 text-primary"></i>
                            <span class="text-muted">{{ site_config.company_address }}</span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-phone me-3 text-primary"></i>
                            <a href="tel:{{ site_config.company_phone }}" class="text-muted text-decoration-none">
                                {{ site_config.company_phone }}
                            </a>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-envelope me-3 text-primary"></i>
                            <a href="mailto:{{ site_config.contact_email }}" class="text-muted text-decoration-none">
                                {{ site_config.contact_email }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="text-muted mb-0">&copy; 2024 {{ site_config.site_name }}. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Floating Action Button -->
    <div class="fab-container">
        <button class="fab" onclick="scrollToTop()" title="返回顶部">
            <i class="fas fa-arrow-up"></i>
        </button>
    </div>

    <!-- Modern JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <script>
        // Theme Toggle
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-bs-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            const themeIcon = document.getElementById('theme-icon');

            html.setAttribute('data-bs-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            if (newTheme === 'dark') {
                themeIcon.className = 'fas fa-sun';
            } else {
                themeIcon.className = 'fas fa-moon';
            }
        }

        // Load saved theme
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            const themeIcon = document.getElementById('theme-icon');

            document.documentElement.setAttribute('data-bs-theme', savedTheme);

            if (savedTheme === 'dark') {
                themeIcon.className = 'fas fa-sun';
            } else {
                themeIcon.className = 'fas fa-moon';
            }
        });

        // Scroll to top
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Show/hide FAB based on scroll position
        window.addEventListener('scroll', function() {
            const fab = document.querySelector('.fab-container');
            if (window.pageYOffset > 300) {
                fab.style.opacity = '1';
                fab.style.visibility = 'visible';
            } else {
                fab.style.opacity = '0';
                fab.style.visibility = 'hidden';
            }
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar-glass');
            if (window.pageYOffset > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Auto-dismiss alerts
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert-modern');
            alerts.forEach(alert => {
                setTimeout(() => {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });

        // Search enhancement
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.addEventListener('focus', function() {
                    this.parentElement.classList.add('search-focused');
                });

                searchInput.addEventListener('blur', function() {
                    this.parentElement.classList.remove('search-focused');
                });
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
