{% extends "admin/base.html" %}

{% block title %}订单管理 - 管理后台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">订单管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportOrders()">
                <i class="fas fa-download me-1"></i>导出订单
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card border-0 bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h6">总订单</div>
                        <div class="h4">{{ stats.total }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-shopping-cart fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card border-0 bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h6">待付款</div>
                        <div class="h4">{{ stats.pending }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card border-0 bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h6">已付款</div>
                        <div class="h4">{{ stats.paid }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-credit-card fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card border-0 bg-secondary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h6">处理中</div>
                        <div class="h4">{{ stats.processing }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-cog fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card border-0 bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h6">已发货</div>
                        <div class="h4">{{ stats.shipped }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-truck fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card border-0 bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h6">已完成</div>
                        <div class="h4">{{ stats.delivered }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 筛选工具栏 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ url_for('admin.orders') }}">
            <div class="row align-items-end">
                <div class="col-md-3">
                    <label for="search" class="form-label">搜索订单</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ search }}" placeholder="订单号、用户名、邮箱">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">订单状态</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">全部状态</option>
                        <option value="pending" {% if current_status == 'pending' %}selected{% endif %}>待付款</option>
                        <option value="paid" {% if current_status == 'paid' %}selected{% endif %}>已付款</option>
                        <option value="processing" {% if current_status == 'processing' %}selected{% endif %}>处理中</option>
                        <option value="shipped" {% if current_status == 'shipped' %}selected{% endif %}>已发货</option>
                        <option value="delivered" {% if current_status == 'delivered' %}selected{% endif %}>已完成</option>
                        <option value="cancelled" {% if current_status == 'cancelled' %}selected{% endif %}>已取消</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                    <a href="{{ url_for('admin.orders') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>清除
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 订单列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">订单列表</h5>
    </div>
    <div class="card-body p-0">
        {% if orders.items %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>订单号</th>
                        <th>用户</th>
                        <th>商品</th>
                        <th>金额</th>
                        <th>状态</th>
                        <th>下单时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for order in orders.items %}
                    <tr>
                        <td>
                            <span class="font-monospace">{{ order.order_no }}</span>
                        </td>
                        <td>
                            <div>
                                <strong>{{ order.user.real_name or order.user.username }}</strong>
                                <br>
                                <small class="text-muted">{{ order.user.email }}</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                {% set first_item = order.order_items.first() %}
                                {% if first_item %}
                                    {{ first_item.product_name }}
                                    {% if order.order_items.count() > 1 %}
                                    <br><small class="text-muted">等{{ order.order_items.count() }}件商品</small>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">无商品信息</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <strong class="text-primary">¥{{ "%.2f"|format(order.final_amount) }}</strong>
                        </td>
                        <td>
                            <select class="form-select form-select-sm status-select" 
                                    data-order-id="{{ order.id }}" 
                                    data-current-status="{{ order.status }}">
                                <option value="pending" {% if order.status == 'pending' %}selected{% endif %}>待付款</option>
                                <option value="paid" {% if order.status == 'paid' %}selected{% endif %}>已付款</option>
                                <option value="processing" {% if order.status == 'processing' %}selected{% endif %}>处理中</option>
                                <option value="shipped" {% if order.status == 'shipped' %}selected{% endif %}>已发货</option>
                                <option value="delivered" {% if order.status == 'delivered' %}selected{% endif %}>已完成</option>
                                <option value="cancelled" {% if order.status == 'cancelled' %}selected{% endif %}>已取消</option>
                            </select>
                        </td>
                        <td>
                            <span>{{ format_china_time(order.created_at, '%Y-%m-%d') }}</span>
                            <br>
                            <small class="text-muted">{{ format_china_time(order.created_at, '%H:%M') }}</small>
                        </td>
                        <td>
                            <a href="{{ url_for('admin.order_detail', id=order.id) }}" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i> 详情
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {% if orders.pages > 1 %}
        <div class="card-footer">
            <nav aria-label="订单分页">
                {{ render_pagination(orders, 'admin.orders', search=search, status=current_status) }}
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">暂无订单数据</h5>
            <p class="text-muted">系统中还没有任何订单记录</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 订单状态更新
    $('.status-select').on('change', function() {
        const orderId = $(this).data('order-id');
        const newStatus = $(this).val();
        const currentStatus = $(this).data('current-status');
        const selectElement = $(this);
        
        if (newStatus === currentStatus) {
            return;
        }
        
        if (!confirm('确定要更改订单状态吗？')) {
            $(this).val(currentStatus);
            return;
        }
        
        // 禁用选择器
        selectElement.prop('disabled', true);
        
        $.ajax({
            url: `/admin/orders/${orderId}/update-status`,
            method: 'POST',
            contentType: 'application/json',
            headers: {
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
                              '{{ csrf_token() }}'
            },
            data: JSON.stringify({
                status: newStatus
            }),
            success: function(response) {
                if (response.success) {
                    selectElement.data('current-status', newStatus);
                    showAlert('success', response.message);
                } else {
                    selectElement.val(currentStatus);
                    showAlert('danger', response.message);
                }
            },
            error: function() {
                selectElement.val(currentStatus);
                showAlert('danger', '状态更新失败，请重试');
            },
            complete: function() {
                selectElement.prop('disabled', false);
            }
        });
    });
});

function exportOrders() {
    // 实现订单导出功能
    showAlert('info', '导出功能正在开发中...');
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(alertHtml);
    
    // 3秒后自动关闭
    setTimeout(function() {
        $('.alert').alert('close');
    }, 3000);
}
</script>
{% endblock %}
