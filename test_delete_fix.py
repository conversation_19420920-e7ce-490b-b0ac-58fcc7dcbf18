#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试：验证属性组删除修复
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from models.product import AttributeGroup, Attribute
    from extensions import db
    
    print("模块导入成功")
    
    app = create_app()
    
    with app.app_context():
        print("Flask应用上下文创建成功")
        
        # 获取所有属性组
        groups = AttributeGroup.query.all()
        print(f"总属性组数量: {len(groups)}")
        
        # 检查每个属性组的属性数量
        for group in groups[:5]:  # 只检查前5个
            attr_count = group.attributes.count()
            print(f"属性组 {group.id} ({group.name}): {attr_count} 个属性")
            
            # 检查删除逻辑
            if attr_count == 0:
                print(f"  -> 属性组 {group.name} 可以删除")
            else:
                attrs = group.attributes.all()
                print(f"  -> 属性组 {group.name} 有属性，不能删除:")
                for attr in attrs[:3]:  # 只显示前3个属性
                    print(f"     - {attr.name}: {attr.value}")
                    
except ImportError as e:
    print(f"导入错误: {e}")
except Exception as e:
    print(f"其他错误: {e}")
    import traceback
    traceback.print_exc() 