{% extends "admin/base.html" %}

{% block title %}属性组管理 - 管理后台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        大属性组管理
        {% if selected_category %}
        <small class="text-muted">- {{ selected_category.name }}</small>
        {% endif %}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('admin.add_attribute_group') }}{% if category_id %}?category_id={{ category_id }}{% endif %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>添加大属性组
        </a>
    </div>
</div>

<!-- 分类筛选 -->
<div class="card mb-3">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <label for="categoryFilter" class="form-label me-3 mb-0">选择分类:</label>
                    <select id="categoryFilter" class="form-select me-3" style="width: auto;" onchange="handleCategoryChange(this.value)">
                        <option value="">请选择分类</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if category_id == category.id %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                    <button type="button" id="refreshBtn" class="btn btn-outline-primary" onclick="window.location.reload()">
                        <i class="fas fa-sync-alt me-1"></i>刷新
                    </button>
                </div>
            </div>
            <div class="col-md-4 text-end">
                {% if selected_category %}
                <a href="{{ url_for('admin.categories') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回分类管理
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if show_empty_state %}
        <!-- 默认空状态 -->
        <div class="text-center py-5">
            <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
            <h5>请选择分类查看大属性组</h5>
            <p class="text-muted">请从上方下拉框中选择一个分类，然后查看该分类下的大属性组</p>
            <div class="mt-3">
                <i class="fas fa-info-circle text-info me-2"></i>
                <small class="text-muted">大属性组用于组织商品的属性选项，每个分类可以有多个大属性组</small>
            </div>
        </div>

        {% elif groups and groups.items %}
        <!-- 有数据时显示表格 -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0">{{ selected_category.name }} - 大属性组列表</h6>
            <span class="badge bg-primary">共 {{ groups.total }} 个</span>
        </div>

        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>大属性组名称</th>
                        <th>描述</th>
                        <th>小属性数量</th>
                        <th>排序</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for group in groups.items %}
                    <tr>
                        <td>{{ group.id }}</td>
                        <td>{{ group.name }}</td>
                        <td>{{ group.description[:50] + '...' if group.description and group.description|length > 50 else group.description or '-' }}</td>
                        <td>
                            {% set attr_count = group.get_attributes_count() %}
                            <span class="badge bg-info">{{ attr_count }}</span>
                            {% if attr_count > 0 %}
                            <a href="{{ url_for('admin.attributes', group_id=group.id, from_group=group.id, locked=True) }}" 
                               class="btn btn-sm btn-outline-info ms-1">
                                <i class="fas fa-eye me-1"></i>查看小属性
                            </a>
                            {% endif %}
                        </td>
                        <td>{{ group.sort_order }}</td>
                        <td>
                            <span class="badge bg-{{ 'success' if group.is_active else 'secondary' }}">
                                {{ '启用' if group.is_active else '禁用' }}
                            </span>
                        </td>
                        <td>{{ group.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('admin.add_attribute', group_id=group.id, from_group=group.id, locked=True) }}" 
                                   class="btn btn-sm btn-success" title="添加小属性">
                                    <i class="fas fa-plus"></i>
                                </a>
                                <a href="{{ url_for('admin.edit_attribute_group', id=group.id) }}" class="btn btn-sm btn-outline-primary" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger delete-btn" title="删除"
                                        data-url="{{ url_for('admin.delete_attribute_group', id=group.id) }}"
                                        data-name="{{ group.name }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {% if groups.pages > 1 %}
        <nav aria-label="属性组分页">
            <ul class="pagination justify-content-center">
                {% if groups.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.attribute_groups', page=groups.prev_num, category_id=category_id) }}">上一页</a>
                </li>
                {% endif %}

                {% for page_num in groups.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != groups.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.attribute_groups', page=page_num, category_id=category_id) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if groups.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.attribute_groups', page=groups.next_num, category_id=category_id) }}">下一页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <!-- 选择了分类但没有数据 -->
        <div class="text-center py-5">
            <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
            <h5>{{ selected_category.name }} 暂无大属性组</h5>
            <p class="text-muted">还没有为该分类创建任何大属性组</p>
            <a href="{{ url_for('admin.add_attribute_group') }}?category_id={{ category_id }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>为该分类添加第一个大属性组
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除属性组 "<span id="deleteName"></span>" 吗？</p>
                <p class="text-danger small">删除后无法恢复，请谨慎操作。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 全局函数：处理分类变化
function handleCategoryChange(categoryId) {
    console.log('🔍 分类选择变化:', categoryId);

    // 构建新的URL
    let newUrl = window.location.pathname;
    if (categoryId && categoryId !== '') {
        newUrl += '?category_id=' + categoryId;
    }

    console.log('🔗 跳转到:', newUrl);

    // 立即跳转
    window.location.href = newUrl;
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 初始化大属性组页面删除功能');
    
    // 初始化删除功能
    initDeleteFunction();
});

function initDeleteFunction() {
    const deleteModal = document.getElementById('deleteModal');
    const deleteNameSpan = document.getElementById('deleteName');
    const confirmDeleteBtn = document.getElementById('confirmDelete');
    let currentDeleteUrl = '';
    let currentDeleteName = '';
    
    if (!deleteModal) {
        console.error('删除模态框未找到');
        return;
    }
    
    const modal = new bootstrap.Modal(deleteModal);
    
    // 移除所有现有的事件监听器
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(btn => {
        // 克隆节点以移除所有事件监听器
        const newBtn = btn.cloneNode(true);
        btn.parentNode.replaceChild(newBtn, btn);
    });
    
    // 重新绑定删除按钮事件
    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            currentDeleteUrl = this.dataset.url;
            currentDeleteName = this.dataset.name;
            
            console.log('点击删除按钮:', currentDeleteName, currentDeleteUrl);
            
            if (deleteNameSpan) {
                deleteNameSpan.textContent = currentDeleteName;
            }
            
            modal.show();
        });
    });
    
    // 确认删除事件
    if (confirmDeleteBtn) {
        // 移除旧的事件监听器
        const newConfirmBtn = confirmDeleteBtn.cloneNode(true);
        confirmDeleteBtn.parentNode.replaceChild(newConfirmBtn, confirmDeleteBtn);
        
        // 绑定新的事件监听器
        document.getElementById('confirmDelete').addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            if (!currentDeleteUrl) {
                showAlert('danger', '删除URL无效');
                return;
            }
            
            // 禁用按钮，显示加载状态
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>删除中...';
            
            // 获取CSRF token
            const csrfToken = getCSRFToken();
            
            console.log('发送删除请求:', currentDeleteUrl);
            console.log('CSRF Token:', csrfToken);
            
            // 发送删除请求
            fetch(currentDeleteUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken,
                    'Accept': 'application/json'
                },
                credentials: 'same-origin'
            })
            .then(response => {
                console.log('响应状态:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return response.json();
            })
            .then(data => {
                console.log('删除响应:', data);
                
                if (data.success) {
                    showAlert('success', data.message || '删除成功');
                    // 延迟刷新页面
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showAlert('danger', data.message || '删除失败');
                    this.disabled = false;
                    this.innerHTML = '确认删除';
                }
            })
            .catch(error => {
                console.error('删除请求错误:', error);
                showAlert('danger', `删除失败: ${error.message}`);
                this.disabled = false;
                this.innerHTML = '确认删除';
            });
            
            // 隐藏模态框
            modal.hide();
        });
    }
}

function getCSRFToken() {
    // 尝试多种方式获取CSRF token
    const metaToken = document.querySelector('meta[name="csrf-token"]');
    if (metaToken) {
        return metaToken.getAttribute('content');
    }
    
    const inputToken = document.querySelector('input[name="csrf_token"]');
    if (inputToken) {
        return inputToken.value;
    }
    
    // 最后尝试模板变量
    return '{{ csrf_token() }}';
}

function showAlert(type, message) {
    // 移除现有的alert
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());
    
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert" style="margin-bottom: 1rem;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertAdjacentHTML('afterbegin', alertHtml);
        
        // 滚动到顶部显示消息
        window.scrollTo({ top: 0, behavior: 'smooth' });
        
        // 自动关闭
        setTimeout(() => {
            const alert = document.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}
</script>
{% endblock %}
