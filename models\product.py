import json
from datetime import datetime
from . import db

# 注释：现在属性组直接属于分类，不再需要关联表

class Category(db.Model):
    __tablename__ = 'categories'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    parent_id = db.Column(db.Integer, db.ForeignKey('categories.id'), index=True)
    sort_order = db.Column(db.Integer, default=0, index=True)
    is_active = db.Column(db.<PERSON><PERSON>an, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    parent = db.relationship('Category', remote_side=[id], backref='children')
    products = db.relationship('Product', backref='category', lazy='dynamic')
    quantity_discounts = db.relationship('QuantityDiscount', backref='category', lazy='dynamic')

    # 分类的属性组（直接关系）
    attribute_groups = db.relationship('AttributeGroup', backref='category', lazy='dynamic', cascade='all, delete-orphan')

    def get_attribute_groups_count(self):
        """获取属性组数量"""
        try:
            return self.attribute_groups.count()
        except:
            return 0
    
    def get_all_children(self):
        """获取所有子分类（递归）"""
        children = []
        for child in self.children:
            children.append(child)
            children.extend(child.get_all_children())
        return children
    
    def get_breadcrumb(self):
        """获取面包屑导航"""
        breadcrumb = []
        current = self
        while current:
            breadcrumb.insert(0, current)
            current = current.parent
        return breadcrumb
    
    def __repr__(self):
        return f'<Category {self.name}>'

class AttributeGroup(db.Model):
    __tablename__ = 'attribute_groups'

    id = db.Column(db.Integer, primary_key=True)
    # 暂时设为可空，等待数据库迁移
    category_id = db.Column(db.Integer, db.ForeignKey('categories.id'), nullable=True, index=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    display_type = db.Column(db.Enum('radio', 'select'), default='radio', nullable=False)  # 显示类型：radio=单选按钮，select=下拉框
    sort_order = db.Column(db.Integer, default=0, index=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    attributes = db.relationship('Attribute', backref='group', lazy='dynamic', cascade='all, delete-orphan')

    def get_attributes_count(self):
        """获取属性数量"""
        try:
            return self.attributes.count()
        except:
            return 0

    def get_display_type_display(self):
        """获取显示类型的中文名称"""
        if self.display_type == 'radio':
            return '单选按钮'
        elif self.display_type == 'select':
            return '下拉框'
        else:
            return '未知'

    def __repr__(self):
        return f'<AttributeGroup {self.name}>'

class Attribute(db.Model):
    __tablename__ = 'attributes'
    
    id = db.Column(db.Integer, primary_key=True)
    group_id = db.Column(db.Integer, db.ForeignKey('attribute_groups.id'), nullable=False, index=True)
    name = db.Column(db.String(100), nullable=False)
    value = db.Column(db.String(200), nullable=False)
    price_modifier = db.Column(db.Numeric(15, 5), default=0.00000)
    price_modifier_type = db.Column(db.Enum('fixed', 'percentage'), default='fixed')
    
    # 公式计算相关字段
    price_formula = db.Column(db.Text, default=None)  # 价格计算公式
    is_quantity_based = db.Column(db.Boolean, default=False)  # 是否支持用户输入数量
    quantity_unit = db.Column(db.String(20), default='个')  # 数量单位
    min_quantity = db.Column(db.Integer, default=1)  # 最小数量
    max_quantity = db.Column(db.Integer, default=9999)  # 最大数量
    
    sort_order = db.Column(db.Integer, default=0, index=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    product_attributes = db.relationship('ProductAttribute', backref='attribute', lazy='dynamic', cascade='all, delete-orphan')
    
    def calculate_formula_price(self, quantity=1, base_price=0):
        """根据公式计算价格"""
        if not self.price_formula or not self.is_quantity_based:
            return 0
        
        try:
            # 安全的公式计算环境
            safe_dict = {
                'quantity': max(self.min_quantity, min(self.max_quantity, quantity)),
                'base_price': base_price,
                'abs': abs,
                'max': max,
                'min': min,
                'round': round,
                '__builtins__': {}  # 禁用内置函数以提高安全性
            }
            
            # 计算公式结果
            result = eval(self.price_formula, safe_dict)
            return max(0, float(result))  # 确保结果不为负数
            
        except Exception as e:
            print(f"公式计算错误 - 属性ID: {self.id}, 公式: {self.price_formula}, 错误: {str(e)}")
            return 0
    
    def get_price_modifier_display(self):
        """获取价格调整显示文本"""
        if self.is_quantity_based and self.price_formula:
            return f"公式: {self.price_formula}"
        elif self.price_modifier == 0:
            return ""
        elif self.price_modifier_type == 'percentage':
            return f"+{self.price_modifier:.5f}%"
        else:
            return f"+¥{self.price_modifier:.5f}"
    
    def get_calculation_type(self):
        """获取计算类型"""
        if self.is_quantity_based and self.price_formula:
            return 'formula'
        elif self.price_modifier_type == 'percentage':
            return 'percentage'
        else:
            return 'fixed'
    
    def __repr__(self):
        return f'<Attribute {self.name}: {self.value}>'

class Product(db.Model):
    __tablename__ = 'products'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    category_id = db.Column(db.Integer, db.ForeignKey('categories.id'), index=True)
    base_price = db.Column(db.Numeric(15, 5), nullable=False)
    min_quantity = db.Column(db.Integer, default=1)
    max_quantity = db.Column(db.Integer, default=999999)
    unit = db.Column(db.String(20), default='个')
    image_url = db.Column(db.String(500))
    images = db.Column(db.JSON)
    is_active = db.Column(db.Boolean, default=True, index=True)
    is_featured = db.Column(db.Boolean, default=False, index=True)
    sort_order = db.Column(db.Integer, default=0, index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    product_attributes = db.relationship('ProductAttribute', backref='product', lazy='dynamic', cascade='all, delete-orphan')
    quantity_discounts = db.relationship('QuantityDiscount', backref='product', lazy='dynamic', cascade='all, delete-orphan')
    order_items = db.relationship('OrderItem', backref='product', lazy='dynamic')

    def get_images_list(self):
        """获取图片列表"""
        if self.images:
            return json.loads(self.images) if isinstance(self.images, str) else self.images
        return []

    def get_main_image(self):
        """获取主图"""
        if self.image_url:
            return self.image_url
        images = self.get_images_list()
        # 使用一个在线的默认图片或者返回空字符串让前端处理
        return images[0] if images else 'https://via.placeholder.com/300x300/f8f9fa/6c757d?text=暂无图片'

    def get_attributes_by_group(self):
        """按组获取属性（确保去重）"""
        result = {}
        seen_attr_ids = set()  # 用于跟踪已处理的属性ID
        
        for pa in self.product_attributes:
            # 避免重复的属性
            if pa.attribute_id in seen_attr_ids:
                continue
            seen_attr_ids.add(pa.attribute_id)
            
            group_name = pa.attribute.group.name
            if group_name not in result:
                result[group_name] = []
            result[group_name].append(pa.attribute)
        return result

    def calculate_price(self, quantity=1, selected_attributes=None):
        """计算价格"""
        base_price = float(self.base_price)
        unit_price = base_price

        # 添加属性价格调整
        if selected_attributes:
            for attr_id in selected_attributes:
                attr = Attribute.query.get(attr_id)
                if attr:
                    if attr.price_modifier_type == 'percentage':
                        # 百分比调整：如果基础价格为0，则百分比调整也为0
                        if base_price > 0:
                            unit_price += base_price * (float(attr.price_modifier) / 100)
                    else:
                        # 固定金额调整：直接加上调整值
                        unit_price += float(attr.price_modifier)

        # 确保单价不小于0
        unit_price = max(0, unit_price)

        # 计算总价（数量 × 单价）
        total_price = unit_price * quantity

        # 应用数量折扣（基于总价）
        discount = self.get_quantity_discount(quantity)
        if discount:
            if discount.discount_type == 'percentage':
                total_price -= total_price * (float(discount.discount_value) / 100)
            else:
                # 固定金额折扣
                total_price -= float(discount.discount_value)

        return max(0, total_price)

    def get_quantity_discount(self, quantity):
        """获取数量折扣"""
        from .order import QuantityDiscount

        # 先查找商品特定折扣
        discount = QuantityDiscount.query.filter(
            QuantityDiscount.product_id == self.id,
            QuantityDiscount.min_quantity <= quantity,
            db.or_(QuantityDiscount.max_quantity.is_(None), QuantityDiscount.max_quantity >= quantity),
            QuantityDiscount.is_active == True
        ).order_by(QuantityDiscount.min_quantity.desc()).first()

        if not discount and self.category_id:
            # 查找分类折扣
            discount = QuantityDiscount.query.filter(
                QuantityDiscount.category_id == self.category_id,
                QuantityDiscount.min_quantity <= quantity,
                db.or_(QuantityDiscount.max_quantity.is_(None), QuantityDiscount.max_quantity >= quantity),
                QuantityDiscount.is_active == True
            ).order_by(QuantityDiscount.min_quantity.desc()).first()

        return discount

    def get_all_quantity_discounts(self):
        """获取所有数量折扣规则"""
        from .order import QuantityDiscount

        # 获取商品特定折扣
        product_discounts = QuantityDiscount.query.filter(
            QuantityDiscount.product_id == self.id,
            QuantityDiscount.is_active == True
        ).order_by(QuantityDiscount.min_quantity).all()

        # 获取分类折扣
        category_discounts = []
        if self.category_id:
            category_discounts = QuantityDiscount.query.filter(
                QuantityDiscount.category_id == self.category_id,
                QuantityDiscount.is_active == True
            ).order_by(QuantityDiscount.min_quantity).all()

        return {
            'product_discounts': product_discounts,
            'category_discounts': category_discounts
        }

    def __repr__(self):
        return f'<Product {self.name}>'
