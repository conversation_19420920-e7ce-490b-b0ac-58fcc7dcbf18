import json
from datetime import datetime
from . import db

class SystemConfig(db.Model):
    __tablename__ = 'system_configs'
    
    id = db.Column(db.Integer, primary_key=True)
    config_key = db.Column(db.String(100), unique=True, nullable=False, index=True)
    config_value = db.Column(db.Text)
    description = db.Column(db.String(500))
    group_name = db.Column(db.String(50), default='其他')  # 配置分组
    config_type = db.Column(db.String(20), default='text')  # 配置类型：text, number, boolean, email, url
    is_required = db.Column(db.<PERSON><PERSON><PERSON>, default=False)  # 是否必填
    validation_rules = db.Column(db.JSON)  # 验证规则，如 {"min": 0, "max": 100}
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    @staticmethod
    def get_config(key, default_value=None):
        """获取配置值"""
        config = SystemConfig.query.filter_by(config_key=key).first()
        if config:
            # 根据类型转换配置值
            if config.config_type == 'number':
                try:
                    return float(config.config_value)
                except (ValueError, TypeError):
                    return default_value
            elif config.config_type == 'boolean':
                return config.config_value.lower() in ('true', '1', 'yes', 'on')
            return config.config_value
        return default_value
    
    @staticmethod
    def set_config(key, value, description=None, group_name=None, config_type=None):
        """设置配置值"""
        config = SystemConfig.query.filter_by(config_key=key).first()
        if config:
            config.config_value = str(value)
            if description:
                config.description = description
            if group_name:
                config.group_name = group_name
            if config_type:
                config.config_type = config_type
            config.updated_at = datetime.utcnow()
        else:
            config = SystemConfig(
                config_key=key,
                config_value=str(value),
                description=description,
                group_name=group_name or '其他',
                config_type=config_type or 'text'
            )
            db.session.add(config)
        
        db.session.commit()
        return config
    
    @staticmethod
    def get_configs_by_group():
        """按组获取所有配置"""
        configs = SystemConfig.query.order_by(SystemConfig.group_name, SystemConfig.id).all()
        grouped_configs = {}
        for config in configs:
            if config.group_name not in grouped_configs:
                grouped_configs[config.group_name] = []
            grouped_configs[config.group_name].append(config)
        return grouped_configs
    
    @staticmethod
    def get_all_configs():
        """获取所有配置"""
        configs = {}
        for config in SystemConfig.query.all():
            configs[config.config_key] = config.config_value
        return configs
    
    def validate_value(self, value):
        """验证配置值是否符合规则"""
        if self.is_required and not value:
            return False, "此项为必填项"
        
        if self.config_type == 'number':
            try:
                num_value = float(value)
                if self.validation_rules:
                    if 'min' in self.validation_rules and num_value < self.validation_rules['min']:
                        return False, f"值不能小于{self.validation_rules['min']}"
                    if 'max' in self.validation_rules and num_value > self.validation_rules['max']:
                        return False, f"值不能大于{self.validation_rules['max']}"
            except (ValueError, TypeError):
                return False, "请输入有效的数字"
        
        elif self.config_type == 'email':
            import re
            if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', value):
                return False, "请输入有效的邮箱地址"
        
        elif self.config_type == 'url':
            import re
            if not re.match(r'^https?://[^\s]+$', value):
                return False, "请输入有效的URL地址"
        
        return True, ""
    
    def __repr__(self):
        return f'<SystemConfig {self.config_key}: {self.config_value}>'
