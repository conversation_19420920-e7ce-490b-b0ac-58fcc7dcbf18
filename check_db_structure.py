from app import create_app
from models import db

def check_orders_table():
    """检查orders表的字段结构"""
    app = create_app()
    with app.app_context():
        try:
            # 获取表结构信息
            inspector = db.inspect(db.engine)
            columns = inspector.get_columns('orders')
            
            print('=== orders表实际字段结构 ===')
            for col in columns:
                name = col['name']
                col_type = str(col['type'])
                nullable = 'NULL' if col['nullable'] else 'NOT NULL'
                default = f" DEFAULT {col['default']}" if col['default'] else ""
                print(f'{name}: {col_type} {nullable}{default}')
                
        except Exception as e:
            print(f"检查失败: {str(e)}")

if __name__ == '__main__':
    check_orders_table() 