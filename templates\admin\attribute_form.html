{% extends "admin/base.html" %}

{% block title %}{{ title }} - 管理后台{% endblock %}

{% block content %}

<!-- 简洁的上下文信息 -->
{% if group_id %}
{% set target_group = groups|selectattr('id', 'equalto', group_id)|first %}
{% if target_group %}
<div class="alert alert-info mb-3">
    <h5 class="mb-1">
        <i class="fas fa-plus-circle me-2"></i>{{ title }}
    </h5>
    <p class="mb-0">
        <strong>商品分类：</strong>{{ target_group.category.name if target_group.category else '未分类' }}
        →
        <strong>属性组：</strong>{{ target_group.name }}
    </p>
</div>
{% endif %}
{% else %}
<div class="mb-3">
    <h4><i class="fas fa-plus-circle me-2"></i>{{ title }}</h4>
</div>
        {% endif %}

<!-- 操作按钮 -->
<div class="row mb-3">
    <div class="col-md-6">
        {% if group_id and target_group %}
        <a href="{{ url_for('admin.attributes', group_id=group_id) }}" 
           class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回属性列表
        </a>
        {% else %}
        <a href="{{ url_for('admin.attributes') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回属性管理
        </a>
        {% endif %}
    </div>
    <div class="col-md-6 text-end">
        <a href="{{ url_for('admin.attribute_groups') }}" class="btn btn-outline-secondary">
            <i class="fas fa-layer-group me-2"></i>管理属性组
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>属性信息
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}

                    <!-- 属性组选择 -->
                    <div class="mb-3">
                        <label class="form-label"><strong>所属属性组</strong></label>
                        {% if group_id and target_group %}
                        <!-- 锁定模式：显示目标属性组 -->
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="text" class="form-control" 
                                   value="{{ target_group.category.name if target_group.category else '未分类' }} - {{ target_group.name }}" 
                                   readonly>
                            {{ form.group_id(style="display: none;", value=group_id) }}
                        </div>
                        <div class="form-text text-success">
                            已锁定到 "{{ target_group.name }}" 属性组
                        </div>
                        {% else %}
                        <!-- 常规模式：允许选择 -->
                        {{ form.group_id(class="form-select") }}
                        <div class="form-text">请选择属性所属的属性组</div>
                        {% if form.group_id.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.group_id.errors %}
                            <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}
                        {% endif %}
                    </div>
                    
                    <!-- 属性名称 -->
                            <div class="mb-3">
                        <label class="form-label"><strong>属性名称</strong></label>
                        {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else ""), placeholder="例如：颜色、尺寸、材质等") }}
                        <div class="form-text">属性的名称，用于区分不同的属性类型</div>
                                {% if form.name.errors %}
                        <div class="invalid-feedback">
                                    {% for error in form.name.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                    <!-- 属性值 -->
                            <div class="mb-3">
                        <label class="form-label"><strong>属性值</strong></label>
                        {{ form.value(class="form-control" + (" is-invalid" if form.value.errors else ""), placeholder="例如：红色、大号、棉质等") }}
                        <div class="form-text">属性的具体值，如果留空则使用属性名称作为值</div>
                                {% if form.value.errors %}
                        <div class="invalid-feedback">
                                    {% for error in form.value.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                    </div>
                    
                    <!-- 价格调整 -->
                    <div class="mb-3">
                        <label class="form-label"><strong>价格计算方式</strong></label>
                        
                        <!-- 计算方式选择 -->
                        <div class="row mb-3">
                            <div class="col-12">
                                {{ form.calculation_type.label(class="form-label") }}
                                {{ form.calculation_type(class="form-select", id="calculationType") }}
                                <div class="form-text">选择价格的计算方式</div>
                            </div>
                        </div>
                        
                        <!-- 传统价格调整 -->
                        <div id="traditionalPricing" class="pricing-section">
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.price_modifier_type.label(class="form-label") }}
                                    {{ form.price_modifier_type(class="form-select") }}
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">调整值</label>
                                    <div class="input-group">
                                        <span class="input-group-text">+</span>
                                        {{ form.price_modifier(class="form-control" + (" is-invalid" if form.price_modifier.errors else ""), placeholder="0.00000", step="0.00001") }}
                                    </div>
                                </div>
                            </div>
                            <div class="form-text">设置为0表示不调整价格</div>
                            {% if form.price_modifier.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.price_modifier.errors %}
                                <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <!-- 公式计算 -->
                        <div id="formulaPricing" class="pricing-section" style="display: none;">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-calculator me-2"></i>公式计算模式</h6>
                                <p class="mb-2">支持用户在前台输入自定义数量，按公式计算价格</p>
                                <small><strong>可用变量：</strong>quantity（用户输入数量）、base_price（商品基础价格）</small><br>
                                <small><strong>可用函数：</strong>max()、min()、abs()、round()</small>
                            </div>
                            
                            <!-- 是否启用数量输入 -->
                            <div class="mb-3">
                                <div class="form-check">
                                    {{ form.is_quantity_based(class="form-check-input") }}
                                    {{ form.is_quantity_based.label(class="form-check-label") }}
                                </div>
                                <div class="form-text">启用后用户可以在前台输入自定义数量</div>
                            </div>
                            
                            <!-- 价格公式 -->
                            <div class="mb-3">
                                {{ form.price_formula.label(class="form-label") }}
                                {{ form.price_formula(class="form-control" + (" is-invalid" if form.price_formula.errors else ""), rows="3", placeholder="例如：quantity * 2.25 + 5") }}
                                {% if form.price_formula.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.price_formula.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    <strong>示例公式：</strong><br>
                                    • <code>quantity * 2.25</code> - 每个数量2.25元<br>
                                    • <code>quantity * 1.5 + 10</code> - 每个数量1.5元 + 固定10元<br>
                                    • <code>max(quantity * 0.8, 5)</code> - 每个数量0.8元，最少5元
                                </div>
                            </div>
                            
                            <!-- 数量设置 -->
                            <div class="row">
                                <div class="col-md-4">
                                    {{ form.quantity_unit.label(class="form-label") }}
                                    {{ form.quantity_unit(class="form-control" + (" is-invalid" if form.quantity_unit.errors else "")) }}
                                    {% if form.quantity_unit.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.quantity_unit.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4">
                                    {{ form.min_quantity.label(class="form-label") }}
                                    {{ form.min_quantity(class="form-control" + (" is-invalid" if form.min_quantity.errors else "")) }}
                                    {% if form.min_quantity.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.min_quantity.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4">
                                    {{ form.max_quantity.label(class="form-label") }}
                                    {{ form.max_quantity(class="form-control" + (" is-invalid" if form.max_quantity.errors else "")) }}
                                    {% if form.max_quantity.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.max_quantity.errors %}
                                        <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- 公式测试 -->
                            <div class="mt-3 p-3 bg-light rounded">
                                <h6>公式测试</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <label class="form-label">测试数量</label>
                                        <input type="number" id="testQuantity" class="form-control" value="10" min="1">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">基础价格</label>
                                        <input type="number" id="testBasePrice" class="form-control" value="100" step="0.01">
                                    </div>
                                    <div class="col-md-4 d-flex align-items-end">
                                        <button type="button" class="btn btn-outline-primary" onclick="testFormula()">
                                            <i class="fas fa-calculator me-2"></i>测试计算
                                        </button>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <strong>计算结果：</strong><span id="testResult" class="text-primary ms-2">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 排序和状态 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label"><strong>排序</strong></label>
                            {{ form.sort_order(class="form-control" + (" is-invalid" if form.sort_order.errors else ""), placeholder="0") }}
                            <div class="form-text">数字越小排序越靠前</div>
                                {% if form.sort_order.errors %}
                            <div class="invalid-feedback">
                                    {% for error in form.sort_order.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                                <div class="form-check">
                                    {{ form.is_active(class="form-check-input") }}
                                <label class="form-check-label" for="{{ form.is_active.id }}">
                                    <strong>启用状态</strong>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 提交按钮 -->
                    <div class="d-flex justify-content-between pt-3 border-top">
                        {% if group_id and target_group %}
                        <a href="{{ url_for('admin.attributes', group_id=group_id) }}" 
                           class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>取消
                        </a>
                        {% else %}
                        <a href="{{ url_for('admin.attributes') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>取消
                        </a>
                        {% endif %}
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>保存属性
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>帮助信息
                </h6>
            </div>
            <div class="card-body">
                <h6>属性说明</h6>
                <ul class="small">
                    <li><strong>属性组</strong>：属性的分类，如"颜色"、"尺寸"等</li>
                    <li><strong>属性名称</strong>：属性的类型名称</li>
                    <li><strong>属性值</strong>：具体的属性值</li>
                    <li><strong>价格调整</strong>：选择此属性时的价格变化</li>
                </ul>
                
                <h6 class="mt-3">价格调整类型</h6>
                <ul class="small">
                    <li><strong>固定金额</strong>：增加固定的金额，如+10元</li>
                    <li><strong>百分比</strong>：按基础价格的百分比增加，如+10%</li>
                    <li><strong>计算公式</strong>：用户可输入自定义数量，按公式计算价格</li>
                </ul>
                
                <h6 class="mt-3">公式计算说明</h6>
                <ul class="small">
                    <li><strong>quantity</strong>：用户输入的数量</li>
                    <li><strong>base_price</strong>：商品的基础价格</li>
                    <li>支持函数：max(), min(), abs(), round()</li>
                    <li>示例：<code>quantity * 2.5 + 10</code></li>
                </ul>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('属性表单JavaScript已加载');
    
    // 初始化计算方式切换
    initializeCalculationTypeSwitch();
    
    // 绑定计算方式变化事件
    const calculationType = document.getElementById('calculationType');
    if (calculationType) {
        console.log('找到calculationType元素，当前值:', calculationType.value);
        calculationType.addEventListener('change', handleCalculationTypeChange);
        
        // 页面加载时设置初始状态
        handleCalculationTypeChange();
    } else {
        console.error('未找到calculationType元素');
    }
});

function initializeCalculationTypeSwitch() {
    // 根据现有数据设置初始的calculation_type值
    const isQuantityBased = document.querySelector('input[name="is_quantity_based"]');
    const priceFormula = document.querySelector('textarea[name="price_formula"]');
    const calculationType = document.getElementById('calculationType');
    
    console.log('初始化状态检查:');
    console.log('- isQuantityBased:', isQuantityBased ? isQuantityBased.checked : 'null');
    console.log('- priceFormula:', priceFormula ? priceFormula.value : 'null');
    console.log('- calculationType:', calculationType ? calculationType.value : 'null');
    
    if (calculationType && isQuantityBased && priceFormula) {
        if (isQuantityBased.checked && priceFormula.value.trim()) {
            calculationType.value = 'formula';
            console.log('设置为公式模式');
        } else {
            const priceModifierType = document.querySelector('select[name="price_modifier_type"]');
            if (priceModifierType) {
                calculationType.value = priceModifierType.value;
                console.log('设置为传统模式:', priceModifierType.value);
            }
        }
    }
}

function handleCalculationTypeChange() {
    const calculationType = document.getElementById('calculationType');
    const traditionalPricing = document.getElementById('traditionalPricing');
    const formulaPricing = document.getElementById('formulaPricing');
    
    console.log('处理计算方式变更');
    console.log('- calculationType:', calculationType ? calculationType.value : 'null');
    console.log('- traditionalPricing:', traditionalPricing ? '存在' : 'null');
    console.log('- formulaPricing:', formulaPricing ? '存在' : 'null');
    
    if (!calculationType || !traditionalPricing || !formulaPricing) {
        console.error('缺少必要的DOM元素');
        return;
    }
    
    const selectedType = calculationType.value;
    console.log('选中的类型:', selectedType);
    
    if (selectedType === 'formula') {
        // 显示公式计算界面
        traditionalPricing.style.display = 'none';
        formulaPricing.style.display = 'block';
        console.log('切换到公式模式：隐藏传统定价，显示公式定价');
        
        // 自动启用数量输入
        const isQuantityBased = document.querySelector('input[name="is_quantity_based"]');
        if (isQuantityBased) {
            isQuantityBased.checked = true;
            console.log('已自动启用数量输入');
        }
    } else {
        // 显示传统价格调整界面
        traditionalPricing.style.display = 'block';
        formulaPricing.style.display = 'none';
        console.log('切换到传统模式：显示传统定价，隐藏公式定价');
        
        // 同步价格调整类型
        const priceModifierType = document.querySelector('select[name="price_modifier_type"]');
        if (priceModifierType) {
            priceModifierType.value = selectedType;
            console.log('已同步价格调整类型:', selectedType);
        }
        
        // 禁用数量输入
        const isQuantityBased = document.querySelector('input[name="is_quantity_based"]');
        if (isQuantityBased) {
            isQuantityBased.checked = false;
            console.log('已禁用数量输入');
        }
        
        // 清空公式
        const priceFormula = document.querySelector('textarea[name="price_formula"]');
        if (priceFormula) {
            priceFormula.value = '';
            console.log('已清空公式');
        }
    }
}

function testFormula() {
    const formula = document.querySelector('textarea[name="price_formula"]').value.trim();
    const testQuantity = parseInt(document.getElementById('testQuantity').value) || 10;
    const testBasePrice = parseFloat(document.getElementById('testBasePrice').value) || 100;
    const resultElement = document.getElementById('testResult');
    
    console.log('测试公式:');
    console.log('- formula:', formula);
    console.log('- testQuantity:', testQuantity);
    console.log('- testBasePrice:', testBasePrice);
    
    if (!formula) {
        resultElement.textContent = '请先输入公式';
        resultElement.className = 'text-warning ms-2';
        return;
    }
    
    try {
        // 创建安全的计算环境
        const safeEnv = {
            quantity: testQuantity,
            base_price: testBasePrice,
            abs: Math.abs,
            max: Math.max,
            min: Math.min,
            round: Math.round
        };
        
        // 使用Function构造器而不是eval，更安全
        const calculate = new Function('env', `
            with(env) {
                return ${formula};
            }
        `);
        
        const result = calculate(safeEnv);
        console.log('计算结果:', result);
        
        if (isNaN(result) || !isFinite(result)) {
            throw new Error('计算结果无效');
        }
        
        resultElement.textContent = `¥${parseFloat(result).toFixed(5)}`;
        resultElement.className = 'text-success ms-2';
        
        // 显示计算详情
        const detailsElement = document.createElement('small');
        detailsElement.className = 'text-muted d-block';
        detailsElement.textContent = `(数量: ${testQuantity}, 基础价格: ¥${testBasePrice})`;
        
        // 移除之前的详情
        const oldDetails = resultElement.nextElementSibling;
        if (oldDetails && oldDetails.tagName === 'SMALL') {
            oldDetails.remove();
        }
        
        resultElement.parentNode.insertBefore(detailsElement, resultElement.nextSibling);
        
    } catch (error) {
        console.error('公式计算错误:', error);
        resultElement.textContent = `错误: ${error.message}`;
        resultElement.className = 'text-danger ms-2';
        
        // 移除之前的详情
        const oldDetails = resultElement.nextElementSibling;
        if (oldDetails && oldDetails.tagName === 'SMALL') {
            oldDetails.remove();
        }
    }
}

// 实时公式验证
document.addEventListener('DOMContentLoaded', function() {
    const formulaTextarea = document.querySelector('textarea[name="price_formula"]');
    if (formulaTextarea) {
        formulaTextarea.addEventListener('input', function() {
            // 延迟验证，避免频繁计算
            clearTimeout(this.validationTimer);
            this.validationTimer = setTimeout(() => {
                if (this.value.trim()) {
                    testFormula();
                }
            }, 1000);
        });
        console.log('已绑定公式实时验证');
    }
});
</script>
{% endblock %}
