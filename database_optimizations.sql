-- ================================
-- 数据库优化和功能增强 SQL 脚本
-- 现代化印刷服务系统
-- ================================

-- 1. 创建索引以提升查询性能
-- ================================

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(is_active, is_verified);

-- 产品表索引
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_price ON products(price);
CREATE INDEX IF NOT EXISTS idx_products_status ON products(is_active, is_featured);
CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_products_search ON products USING gin(to_tsvector('chinese', name || ' ' || COALESCE(description, '')));

-- 订单表索引
CREATE INDEX IF NOT EXISTS idx_orders_user ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_orders_total ON orders(total_amount);

-- 购物车表索引
CREATE INDEX IF NOT EXISTS idx_cart_items_user ON cart_items(user_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_product ON cart_items(product_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_created_at ON cart_items(created_at);

-- 分类表索引
CREATE INDEX IF NOT EXISTS idx_categories_parent ON categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_categories_sort ON categories(sort_order);
CREATE INDEX IF NOT EXISTS idx_categories_status ON categories(is_active);

-- 2. 创建有用的视图
-- ================================

-- 产品统计视图
CREATE OR REPLACE VIEW product_stats AS
SELECT 
    p.id,
    p.name,
    p.price,
    p.category_id,
    c.name as category_name,
    COUNT(DISTINCT oi.id) as total_orders,
    COALESCE(SUM(oi.quantity), 0) as total_sold,
    COALESCE(AVG(pr.rating), 0) as avg_rating,
    COUNT(DISTINCT pr.id) as review_count,
    p.created_at
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN order_items oi ON p.id = oi.product_id
LEFT JOIN product_reviews pr ON p.id = pr.product_id
WHERE p.is_active = true
GROUP BY p.id, p.name, p.price, p.category_id, c.name, p.created_at;

-- 用户订单统计视图
CREATE OR REPLACE VIEW user_order_stats AS
SELECT 
    u.id as user_id,
    u.username,
    u.email,
    COUNT(DISTINCT o.id) as total_orders,
    COALESCE(SUM(o.total_amount), 0) as total_spent,
    COALESCE(AVG(o.total_amount), 0) as avg_order_value,
    MAX(o.created_at) as last_order_date,
    COUNT(DISTINCT CASE WHEN o.status = 'completed' THEN o.id END) as completed_orders
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
GROUP BY u.id, u.username, u.email;

-- 销售分析视图
CREATE OR REPLACE VIEW sales_analytics AS
SELECT 
    DATE(o.created_at) as sale_date,
    COUNT(DISTINCT o.id) as order_count,
    COUNT(DISTINCT o.user_id) as customer_count,
    SUM(o.total_amount) as total_revenue,
    AVG(o.total_amount) as avg_order_value,
    SUM(oi.quantity) as total_items_sold
FROM orders o
JOIN order_items oi ON o.id = oi.order_id
WHERE o.status = 'completed'
GROUP BY DATE(o.created_at)
ORDER BY sale_date DESC;

-- 3. 添加缺失的表和字段
-- ================================

-- 产品评价表
CREATE TABLE IF NOT EXISTS product_reviews (
    id SERIAL PRIMARY KEY,
    product_id INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(200),
    comment TEXT,
    is_verified BOOLEAN DEFAULT false,
    helpful_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(product_id, user_id)
);

-- 收藏夹表
CREATE TABLE IF NOT EXISTS wishlists (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    product_id INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, product_id)
);

-- 优惠券表
CREATE TABLE IF NOT EXISTS coupons (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    type VARCHAR(20) NOT NULL CHECK (type IN ('percentage', 'fixed')),
    value DECIMAL(10,2) NOT NULL,
    min_amount DECIMAL(10,2) DEFAULT 0,
    max_discount DECIMAL(10,2),
    usage_limit INTEGER,
    used_count INTEGER DEFAULT 0,
    user_limit INTEGER DEFAULT 1,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户优惠券使用记录
CREATE TABLE IF NOT EXISTS user_coupons (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    coupon_id INTEGER NOT NULL REFERENCES coupons(id) ON DELETE CASCADE,
    order_id INTEGER REFERENCES orders(id) ON DELETE SET NULL,
    used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 商品图片表
CREATE TABLE IF NOT EXISTS product_images (
    id SERIAL PRIMARY KEY,
    product_id INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    url VARCHAR(500) NOT NULL,
    alt_text VARCHAR(200),
    sort_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 产品属性表
CREATE TABLE IF NOT EXISTS product_attributes (
    id SERIAL PRIMARY KEY,
    product_id INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    value VARCHAR(500) NOT NULL,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_settings (
    id SERIAL PRIMARY KEY,
    key VARCHAR(100) UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    type VARCHAR(20) DEFAULT 'string',
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 操作日志表
CREATE TABLE IF NOT EXISTS activity_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50),
    record_id INTEGER,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. 插入基础数据
-- ================================

-- 系统配置
INSERT INTO system_settings (key, value, description, type, is_public) VALUES
    ('site_name', '专业印刷服务', '网站名称', 'string', true),
    ('site_description', '提供高品质的印刷服务解决方案', '网站描述', 'string', true),
    ('contact_phone', '************', '联系电话', 'string', true),
    ('contact_email', '<EMAIL>', '联系邮箱', 'string', true),
    ('min_order_amount', '50.00', '最小订单金额', 'decimal', true),
    ('free_shipping_amount', '200.00', '免运费金额', 'decimal', true),
    ('default_shipping_fee', '15.00', '默认运费', 'decimal', true),
    ('currency_symbol', '￥', '货币符号', 'string', true),
    ('max_cart_items', '99', '购物车最大商品数量', 'integer', false),
    ('session_timeout', '3600', '会话超时时间(秒)', 'integer', false)
ON CONFLICT (key) DO NOTHING;

-- 基础分类数据
INSERT INTO categories (name, description, sort_order, is_active) VALUES
    ('名片印刷', '专业名片设计与印刷服务', 1, true),
    ('宣传册', '企业宣传册、产品手册印刷', 2, true),
    ('海报印刷', '大幅面海报、宣传画印刷', 3, true),
    ('标签贴纸', '各种规格标签、贴纸定制', 4, true),
    ('包装印刷', '产品包装、礼盒印刷', 5, true),
    ('办公用品', '信封、信纸等办公印刷品', 6, true)
ON CONFLICT DO NOTHING;

-- 示例优惠券
INSERT INTO coupons (code, name, description, type, value, min_amount, usage_limit, start_date, end_date) VALUES
    ('WELCOME10', '新用户专享', '新用户首单立减10元', 'fixed', 10.00, 50.00, 1000, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '30 days'),
    ('SAVE5', '满100减5', '订单满100元立减5元', 'fixed', 5.00, 100.00, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '7 days'),
    ('VIP15', 'VIP专享15%', 'VIP用户专享15%折扣', 'percentage', 15.00, 200.00, 500, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '15 days')
ON CONFLICT (code) DO NOTHING;

-- 5. 创建存储过程
-- ================================

-- 计算购物车总额
CREATE OR REPLACE FUNCTION calculate_cart_total(p_user_id INTEGER)
RETURNS TABLE(total_amount DECIMAL, total_items INTEGER) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(SUM(ci.quantity * p.price), 0) as total_amount,
        COALESCE(SUM(ci.quantity), 0)::INTEGER as total_items
    FROM cart_items ci
    JOIN products p ON ci.product_id = p.id
    WHERE ci.user_id = p_user_id AND p.is_active = true;
END;
$$ LANGUAGE plpgsql;

-- 应用优惠券
CREATE OR REPLACE FUNCTION apply_coupon(p_coupon_code VARCHAR, p_order_amount DECIMAL, p_user_id INTEGER)
RETURNS TABLE(is_valid BOOLEAN, discount_amount DECIMAL, message TEXT) AS $$
DECLARE
    v_coupon coupons%ROWTYPE;
    v_user_usage_count INTEGER;
    v_discount DECIMAL := 0;
BEGIN
    -- 查找优惠券
    SELECT * INTO v_coupon FROM coupons WHERE code = p_coupon_code AND is_active = true;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 0::DECIMAL, '优惠券不存在或已失效';
        RETURN;
    END IF;
    
    -- 检查有效期
    IF v_coupon.start_date > CURRENT_TIMESTAMP OR v_coupon.end_date < CURRENT_TIMESTAMP THEN
        RETURN QUERY SELECT false, 0::DECIMAL, '优惠券已过期';
        RETURN;
    END IF;
    
    -- 检查最小金额
    IF p_order_amount < v_coupon.min_amount THEN
        RETURN QUERY SELECT false, 0::DECIMAL, '订单金额不满足使用条件';
        RETURN;
    END IF;
    
    -- 检查使用次数限制
    IF v_coupon.usage_limit IS NOT NULL AND v_coupon.used_count >= v_coupon.usage_limit THEN
        RETURN QUERY SELECT false, 0::DECIMAL, '优惠券使用次数已达上限';
        RETURN;
    END IF;
    
    -- 检查用户使用次数
    SELECT COUNT(*) INTO v_user_usage_count 
    FROM user_coupons 
    WHERE user_id = p_user_id AND coupon_id = v_coupon.id;
    
    IF v_user_usage_count >= v_coupon.user_limit THEN
        RETURN QUERY SELECT false, 0::DECIMAL, '您已使用过此优惠券';
        RETURN;
    END IF;
    
    -- 计算折扣
    IF v_coupon.type = 'percentage' THEN
        v_discount := p_order_amount * v_coupon.value / 100;
        IF v_coupon.max_discount IS NOT NULL THEN
            v_discount := LEAST(v_discount, v_coupon.max_discount);
        END IF;
    ELSE
        v_discount := v_coupon.value;
    END IF;
    
    v_discount := LEAST(v_discount, p_order_amount);
    
    RETURN QUERY SELECT true, v_discount, '优惠券可以使用';
END;
$$ LANGUAGE plpgsql;

-- 更新产品销量
CREATE OR REPLACE FUNCTION update_product_sales()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE products 
        SET sales_count = COALESCE(sales_count, 0) + NEW.quantity
        WHERE id = NEW.product_id;
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        UPDATE products 
        SET sales_count = COALESCE(sales_count, 0) - OLD.quantity + NEW.quantity
        WHERE id = NEW.product_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE products 
        SET sales_count = COALESCE(sales_count, 0) - OLD.quantity
        WHERE id = OLD.product_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 6. 创建触发器
-- ================================

-- 自动更新时间戳
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为有updated_at字段的表创建触发器
DO $$
DECLARE
    table_name TEXT;
    table_names TEXT[] := ARRAY['users', 'products', 'orders', 'categories', 'coupons', 'system_settings'];
BEGIN
    FOREACH table_name IN ARRAY table_names
    LOOP
        EXECUTE format('
            DROP TRIGGER IF EXISTS trigger_update_updated_at ON %I;
            CREATE TRIGGER trigger_update_updated_at
                BEFORE UPDATE ON %I
                FOR EACH ROW
                EXECUTE FUNCTION update_updated_at_column();
        ', table_name, table_name);
    END LOOP;
END $$;

-- 产品销量更新触发器
DROP TRIGGER IF EXISTS trigger_update_product_sales ON order_items;
CREATE TRIGGER trigger_update_product_sales
    AFTER INSERT OR UPDATE OR DELETE ON order_items
    FOR EACH ROW
    EXECUTE FUNCTION update_product_sales();

-- 7. 创建全文搜索配置
-- ================================

-- 创建中文分词配置（如果支持）
-- CREATE TEXT SEARCH CONFIGURATION chinese_config (COPY = simple);

-- 为产品表添加全文搜索
ALTER TABLE products ADD COLUMN IF NOT EXISTS search_vector tsvector;

-- 更新搜索向量
UPDATE products SET search_vector = to_tsvector('chinese', name || ' ' || COALESCE(description, ''));

-- 创建搜索向量更新函数
CREATE OR REPLACE FUNCTION update_product_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('chinese', NEW.name || ' ' || COALESCE(NEW.description, ''));
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建搜索向量更新触发器
DROP TRIGGER IF EXISTS trigger_update_search_vector ON products;
CREATE TRIGGER trigger_update_search_vector
    BEFORE INSERT OR UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION update_product_search_vector();

-- 8. 数据备份和恢复建议
-- ================================

-- 创建备份函数（需要超级用户权限）
/*
CREATE OR REPLACE FUNCTION backup_database(backup_path TEXT)
RETURNS TEXT AS $$
DECLARE
    backup_file TEXT;
BEGIN
    backup_file := backup_path || '/backup_' || to_char(CURRENT_TIMESTAMP, 'YYYY_MM_DD_HH24_MI_SS') || '.sql';
    EXECUTE format('COPY (SELECT pg_dump($1)) TO %L', backup_file);
    RETURN backup_file;
END;
$$ LANGUAGE plpgsql;
*/

-- 9. 性能监控查询
-- ================================

-- 查看慢查询
CREATE OR REPLACE VIEW slow_queries AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
ORDER BY mean_time DESC;

-- 查看表大小
CREATE OR REPLACE VIEW table_sizes AS
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size,
    pg_total_relation_size(schemaname||'.'||tablename) AS size_bytes
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 10. 清理和维护
-- ================================

-- 清理过期会话
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 假设有一个sessions表
    -- DELETE FROM sessions WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '1 day';
    -- GET DIAGNOSTICS deleted_count = ROW_COUNT;
    deleted_count := 0; -- 占位符
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 清理过期购物车
CREATE OR REPLACE FUNCTION cleanup_expired_cart_items()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM cart_items WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '30 days';
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 统计信息更新
CREATE OR REPLACE FUNCTION update_table_statistics()
RETURNS TEXT AS $$
BEGIN
    ANALYZE;
    RETURN 'Statistics updated for all tables';
END;
$$ LANGUAGE plpgsql;

-- ================================
-- 优化完成提示
-- ================================

SELECT 'Database optimization completed successfully!' AS message; 