#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试脚本：检查属性组和属性的关系
用于诊断删除属性组时的问题
"""

from app import create_app
from models.product import AttributeGroup, Attribute
from extensions import db

def debug_attribute_groups():
    """调试属性组和属性关系"""
    app = create_app()
    
    with app.app_context():
        print("=== 属性组调试信息 ===")
        print()
        
        # 获取所有属性组
        groups = AttributeGroup.query.all()
        print(f"总属性组数量: {len(groups)}")
        print()
        
        for group in groups:
            print(f"属性组 ID: {group.id}")
            print(f"属性组名称: {group.name}")
            print(f"所属分类: {group.category.name if group.category else '无'}")
            
            # 使用三种方法检查属性数量
            method1_count = group.get_attributes_count()  # 模型方法
            method2_count = group.attributes.count()      # 直接count()
            method3_list = group.attributes.all()         # 获取所有属性
            method3_count = len(method3_list)
            
            print(f"方法1 (get_attributes_count): {method1_count}")
            print(f"方法2 (attributes.count): {method2_count}")
            print(f"方法3 (len(attributes.all)): {method3_count}")
            
            if method3_list:
                print("属性详情:")
                for attr in method3_list:
                    print(f"  - 属性ID: {attr.id}, 名称: {attr.name}, 值: {attr.value}")
            else:
                print("  无属性")
            
            # 检查关系对象本身
            print(f"关系对象类型: {type(group.attributes)}")
            print(f"关系对象布尔值: {bool(group.attributes)}")
            
            print("-" * 50)
        
        print()
        print("=== 孤立属性检查 ===")
        
        # 检查是否有孤立的属性（引用了不存在的属性组）
        all_attributes = Attribute.query.all()
        print(f"总属性数量: {len(all_attributes)}")
        
        orphaned_attributes = []
        for attr in all_attributes:
            if not attr.group:
                orphaned_attributes.append(attr)
        
        if orphaned_attributes:
            print(f"发现 {len(orphaned_attributes)} 个孤立属性:")
            for attr in orphaned_attributes:
                print(f"  - 属性ID: {attr.id}, 名称: {attr.name}, group_id: {attr.group_id}")
        else:
            print("未发现孤立属性")
        
        print()
        print("=== 建议操作 ===")
        
        empty_groups = [g for g in groups if g.get_attributes_count() == 0]
        if empty_groups:
            print(f"发现 {len(empty_groups)} 个空属性组，可以删除:")
            for group in empty_groups:
                print(f"  - 属性组ID: {group.id}, 名称: {group.name}")
        else:
            print("未发现空属性组")

if __name__ == '__main__':
    debug_attribute_groups() 