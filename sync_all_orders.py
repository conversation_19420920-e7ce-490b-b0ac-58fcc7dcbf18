#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from models.order import Order
from models import db
from app import create_app
from services.payment import get_payment_service

def sync_all_pending_orders():
    """同步所有待支付订单的状态"""
    app = create_app()
    with app.app_context():
        payment_service = get_payment_service()
        
        # 查找所有待支付的订单
        pending_orders = Order.query.filter_by(
            status='pending', 
            payment_status='pending'
        ).all()
        
        print(f"找到 {len(pending_orders)} 个待支付订单，开始同步...")
        print("=" * 60)
        
        success_count = 0
        error_count = 0
        already_paid_count = 0
        
        for order in pending_orders:
            print(f"\n处理订单: {order.order_no}")
            print(f"订单金额: ¥{order.final_amount}")
            print(f"创建时间: {order.created_at}")
            
            try:
                result = payment_service.sync_payment_status(order.order_no)
                
                if result['success']:
                    if result.get('updated'):
                        success_count += 1
                        print(f"✅ 同步成功: {result['message']}")
                        print(f"   状态更新: {result['old_status']} -> {result['new_status']}")
                        print(f"   支付方式: {result.get('payment_method', 'N/A')}")
                    elif result.get('already_paid'):
                        already_paid_count += 1
                        print(f"ℹ️  订单已是已支付状态")
                    else:
                        print(f"ℹ️  {result['message']}")
                else:
                    error_count += 1
                    print(f"❌ 同步失败: {result.get('error', '未知错误')}")
                    
            except Exception as e:
                error_count += 1
                print(f"❌ 处理异常: {str(e)}")
        
        print("\n" + "=" * 60)
        print("同步结果统计:")
        print(f"总订单数: {len(pending_orders)}")
        print(f"同步成功: {success_count}")
        print(f"已是已支付: {already_paid_count}")
        print(f"同步失败: {error_count}")
        print("=" * 60)

def sync_single_order(order_no):
    """同步单个订单的状态"""
    app = create_app()
    with app.app_context():
        payment_service = get_payment_service()
        
        print(f"同步订单: {order_no}")
        print("=" * 40)
        
        try:
            result = payment_service.sync_payment_status(order_no)
            
            if result['success']:
                if result.get('updated'):
                    print(f"✅ 同步成功: {result['message']}")
                    print(f"状态更新: {result['old_status']} -> {result['new_status']}")
                    print(f"支付状态: {result['old_payment_status']} -> {result['new_payment_status']}")
                    print(f"支付方式: {result.get('payment_method', 'N/A')}")
                elif result.get('already_paid'):
                    print(f"ℹ️  订单已是已支付状态")
                else:
                    print(f"ℹ️  {result['message']}")
            else:
                print(f"❌ 同步失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 处理异常: {str(e)}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # 同步指定订单
        order_no = sys.argv[1]
        sync_single_order(order_no)
    else:
        # 同步所有待支付订单
        sync_all_pending_orders() 